{"compilerOptions": {"target": "ES6", "module": "ESNext", "lib": ["ESNext"], "moduleResolution": "Node", "strict": true, "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "useDefineForClassFields": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}}