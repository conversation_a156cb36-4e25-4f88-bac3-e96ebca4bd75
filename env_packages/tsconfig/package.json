{"name": "@moxo/tsconfig", "version": "1.0.0", "description": "Shared TypeScript configuration for projects", "exports": {"./base": "./tsconfig.base.json", "./web": "./tsconfig.web.json", "./node": "./tsconfig.node.json", "./lib": "./tsconfig.lib.json"}, "files": ["tsconfig.base.json", "tsconfig.web.json", "tsconfig.node.json", "tsconfig.lib.json"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON>", "license": "MIT"}