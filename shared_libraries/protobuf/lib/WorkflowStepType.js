"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowStepType = void 0;
var WorkflowStepType;
(function (WorkflowStepType) {
    WorkflowStepType["WORKFLOW_STEP_TYPE_INVALID"] = "WORKFLOW_STEP_TYPE_INVALID";
    WorkflowStepType["WORKFLOW_STEP_TYPE_MILESTONE"] = "WORKFLOW_STEP_TYPE_MILESTONE";
    WorkflowStepType["WORKFLOW_STEP_TYPE_AB"] = "WORKFLOW_STEP_TYPE_AB";
    WorkflowStepType["WORKFLOW_STEP_TYPE_AUTOMATION"] = "WORKFLOW_STEP_TYPE_AUTOMATION";
    WorkflowStepType["WORKFLOW_STEP_TYPE_CB"] = "WORKFLOW_STEP_TYPE_CB";
    WorkflowStepType["WORKFLOW_STEP_TYPE_SHADOW_FLOW"] = "WORKFLOW_STEP_TYPE_SHADOW_FLOW";
    WorkflowStepType["WORKFLOW_STEP_TYPE_SEND_FILE"] = "WORKFLOW_STEP_TYPE_SEND_FILE";
    WorkflowStepType["WORKFLOW_STEP_TYPE_TRANSACTION"] = "WORKFLOW_STEP_TYPE_TRANSACTION";
    WorkflowStepType["WORKFLOW_STEP_TYPE_FORM_REQUEST"] = "WORKFLOW_STEP_TYPE_FORM_REQUEST";
    WorkflowStepType["WORKFLOW_STEP_TYPE_FILE_REQUEST"] = "WORKFLOW_STEP_TYPE_FILE_REQUEST";
    WorkflowStepType["WORKFLOW_STEP_TYPE_MEET_REQUEST"] = "WORKFLOW_STEP_TYPE_MEET_REQUEST";
    WorkflowStepType["WORKFLOW_STEP_TYPE_APPROVAL"] = "WORKFLOW_STEP_TYPE_APPROVAL";
    WorkflowStepType["WORKFLOW_STEP_TYPE_ACKNOWLEDGE"] = "WORKFLOW_STEP_TYPE_ACKNOWLEDGE";
    WorkflowStepType["WORKFLOW_STEP_TYPE_SIGNATURE"] = "WORKFLOW_STEP_TYPE_SIGNATURE";
    WorkflowStepType["WORKFLOW_STEP_TYPE_TODO"] = "WORKFLOW_STEP_TYPE_TODO";
    WorkflowStepType["WORKFLOW_STEP_TYPE_TIME_BOOKING"] = "WORKFLOW_STEP_TYPE_TIME_BOOKING";
    WorkflowStepType["WORKFLOW_STEP_TYPE_DOCUSIGN"] = "WORKFLOW_STEP_TYPE_DOCUSIGN";
    WorkflowStepType["WORKFLOW_STEP_TYPE_WEBHOOK"] = "WORKFLOW_STEP_TYPE_WEBHOOK";
    WorkflowStepType["WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP"] = "WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP";
    WorkflowStepType["WORKFLOW_STEP_TYPE_INTEGRATION"] = "WORKFLOW_STEP_TYPE_INTEGRATION";
    WorkflowStepType["WORKFLOW_STEP_TYPE_TODO_TRANSACTION"] = "WORKFLOW_STEP_TYPE_TODO_TRANSACTION";
    WorkflowStepType["WORKFLOW_STEP_TYPE_DECISION"] = "WORKFLOW_STEP_TYPE_DECISION";
    WorkflowStepType["WORKFLOW_STEP_TYPE_AWAIT"] = "WORKFLOW_STEP_TYPE_AWAIT";
    WorkflowStepType["WORKFLOW_STEP_TYPE_PDF_FORM"] = "WORKFLOW_STEP_TYPE_PDF_FORM";
    WorkflowStepType["WORKFLOW_STEP_TYPE_SHADOW_ACTION"] = "WORKFLOW_STEP_TYPE_SHADOW_ACTION";
})(WorkflowStepType || (exports.WorkflowStepType = WorkflowStepType = {}));
