"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionType = void 0;
var TransactionType;
(function (TransactionType) {
    TransactionType["TRANSACTION_TYPE_GENERIC"] = "TRANSACTION_TYPE_GENERIC";
    TransactionType["TRANSACTION_TYPE_APPROVAL"] = "TRANSACTION_TYPE_APPROVAL";
    TransactionType["TRANSACTION_TYPE_ACKNOWLEDGE"] = "TRANSACTION_TYPE_ACKNOWLEDGE";
    TransactionType["TRANSACTION_TYPE_FILE_REQUEST"] = "TRANSACTION_TYPE_FILE_REQUEST";
    TransactionType["TRANSACTION_TYPE_MEET_REQUEST"] = "TRANSACTION_TYPE_MEET_REQUEST";
    TransactionType["TRANSACTION_TYPE_FORM_REQUEST"] = "TRANSACTION_TYPE_FORM_REQUEST";
    TransactionType["TRANSACTION_TYPE_TIME_BOOKING"] = "TRANSACTION_TYPE_TIME_BOOKING";
    TransactionType["TRANSACTION_TYPE_PDF_FORM"] = "TRANSACTION_TYPE_PDF_FORM";
    TransactionType["TRANSACTION_TYPE_DOCUSIGN"] = "TRANSACTION_TYPE_DOCUSIGN";
    TransactionType["TRANSACTION_TYPE_WEBHOOK"] = "TRANSACTION_TYPE_WEBHOOK";
    TransactionType["TRANSACTION_TYPE_LAUNCH_WEB_APP"] = "TRANSACTION_TYPE_LAUNCH_WEB_APP";
    TransactionType["TRANSACTION_TYPE_INTEGRATION"] = "TRANSACTION_TYPE_INTEGRATION";
    TransactionType["TRANSACTION_TYPE_TODO"] = "TRANSACTION_TYPE_TODO";
    TransactionType["TRANSACTION_TYPE_DECISION"] = "TRANSACTION_TYPE_DECISION";
    TransactionType["TRANSACTION_TYPE_AWAIT"] = "TRANSACTION_TYPE_AWAIT";
})(TransactionType || (exports.TransactionType = TransactionType = {}));
