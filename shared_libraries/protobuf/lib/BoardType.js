"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BoardType = void 0;
var BoardType;
(function (BoardType) {
    BoardType["BOARD_TYPE_DEFAULT"] = "BOARD_TYPE_DEFAULT";
    BoardType["BOARD_TYPE_WORKFLOW"] = "BOARD_TYPE_WORKFLOW";
    BoardType["BOARD_TYPE_WORKFLOW_TEMPLATE"] = "BOARD_TYPE_WORKFLOW_TEMPLATE";
    BoardType["BOARD_TYPE_CONTENT_LIBRARY_ACTION"] = "BOARD_TYPE_CONTENT_LIBRARY_ACTION";
    BoardType["BOARD_TYPE_CONTENT_LIBRARY_FILE"] = "BOARD_TYPE_CONTENT_LIBRARY_FILE";
    BoardType["BOARD_TYPE_CONTENT_LIBRARY_MILESTONE"] = "BOARD_TYPE_CONTENT_LIBRARY_MILESTONE";
    BoardType["BOARD_TYPE_SCHEDULE"] = "BOARD_TYPE_SCHEDULE";
    BoardType["BOARD_TYPE_SELF_SERVICE_TEMPLATE"] = "BOARD_TYPE_SELF_SERVICE_TEMPLATE";
    BoardType["BOARD_TYPE_WORKFLOW_TEMPLATE_FOLDER"] = "BOARD_TYPE_WORKFLOW_TEMPLATE_FOLDER";
    BoardType["BOARD_TYPE_BROADCAST"] = "BOARD_TYPE_BROADCAST";
    BoardType["BOARD_TYPE_AI"] = "BOARD_TYPE_AI";
})(BoardType || (exports.BoardType = BoardType = {}));
