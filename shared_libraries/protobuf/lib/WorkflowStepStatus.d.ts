export declare enum WorkflowStepStatus {
    WORKFLOW_STEP_STATUS_INITIAL = "WORKFLOW_STEP_STATUS_INITIAL",
    WORKFLOW_STEP_STATUS_PREPARING = "WORKFLOW_STEP_STATUS_PREPARING",
    WORK<PERSON>OW_STEP_STATUS_READY = "WORK<PERSON>OW_STEP_STATUS_READY",
    WORKFLOW_STEP_STATUS_STARTED = "WORKFLOW_STEP_STATUS_STARTED",
    WORKFLOW_STEP_STATUS_COMPLETED = "WORKFLOW_STEP_STATUS_COMPLETED",
    WORK<PERSON>OW_STEP_STATUS_CANCELED = "WORKFLOW_STEP_STATUS_CANCELED",
    WORK<PERSON>OW_STEP_STATUS_FAULTED = "WORKFLOW_STEP_STATUS_FAULTED",
    WORKFLOW_STEP_STATUS_SKIPPED = "WORKFLOW_STEP_STATUS_SKIPPED",
    WORK<PERSON>OW_STEP_STATUS_PRECONDITION_NOT_MET = "WORKFLOW_STEP_STATUS_PRECONDITION_NOT_MET"
}
//# sourceMappingURL=WorkflowStepStatus.d.ts.map