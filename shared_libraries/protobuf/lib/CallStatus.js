"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallStatus = void 0;
var CallStatus;
(function (CallStatus) {
    CallStatus["CALL_STATUS_INVALID"] = "CALL_STATUS_INVALID";
    CallStatus["CALL_STATUS_INITIALIZED"] = "CALL_STATUS_INITIALIZED";
    CallStatus["CALL_STATUS_RINGING"] = "CALL_STATUS_RINGING";
    CallStatus["CALL_STATUS_CONNECTING"] = "CALL_STATUS_CONNECTING";
    CallStatus["CALL_STATUS_CONNECTED"] = "CALL_STATUS_CONNECTED";
    CallStatus["CALL_STATUS_CANCELLED"] = "CALL_STATUS_CANCELLED";
    CallStatus["CALL_STATUS_NOANSWER"] = "CALL_STATUS_NOANSWER";
    CallStatus["CALL_STATUS_DECLINED"] = "CALL_STATUS_DECLINED";
    CallStatus["CALL_STATUS_ENDED"] = "CALL_STATUS_ENDED";
    CallStatus["CALL_STATUS_FAILED"] = "CALL_STATUS_FAILED";
})(CallStatus || (exports.CallStatus = CallStatus = {}));
