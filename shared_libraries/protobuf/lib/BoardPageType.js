"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BoardPageType = void 0;
var BoardPageType;
(function (BoardPageType) {
    BoardPageType["PAGE_TYPE_WHITEBOARD"] = "PAGE_TYPE_WHITEBOARD";
    BoardPageType["PAGE_TYPE_NOT_SUPPORTED"] = "PAGE_TYPE_NOT_SUPPORTED";
    BoardPageType["PAGE_TYPE_IMAGE"] = "PAGE_TYPE_IMAGE";
    BoardPageType["PAGE_TYPE_WEB"] = "PAGE_TYPE_WEB";
    BoardPageType["PAGE_TYPE_VIDEO"] = "PAGE_TYPE_VIDEO";
    BoardPageType["PAGE_TYPE_AUDIO"] = "PAGE_TYPE_AUDIO";
    BoardPageType["PAGE_TYPE_PDF"] = "PAGE_TYPE_PDF";
    BoardPageType["PAGE_TYPE_URL"] = "PAGE_TYPE_URL";
    BoardPageType["PAGE_TYPE_NOTE"] = "PAGE_TYPE_NOTE";
    BoardPageType["PAGE_TYPE_DESKTOPSHARE"] = "PAGE_TYPE_DESKTOPSHARE";
    BoardPageType["PAGE_TYPE_GEO"] = "PAGE_TYPE_GEO";
    BoardPageType["PAGE_TYPE_ANY"] = "PAGE_TYPE_ANY";
})(BoardPageType || (exports.BoardPageType = BoardPageType = {}));
