{"name": "@moxo/proto", "version": "0.0.1", "main": "lib/index.js", "module": "es/index.js", "type": "module", "types": "types/index.d.ts", "files": ["lib", "es", "types", "ProtoBufDefineJSON.ts", "protobuf-static.js"], "sideEffects": false, "license": "MIT", "scripts": {"build:es": "tsc --project tsconfig.json --module esnext --outDir es", "build:lib": "tsc --project tsconfig.json --module commonjs --outDir lib", "test": "cross-env NODE_ENV=test jest --config .jest.js", "build": "node ./scripts/cli.mjs build --source boarddef.proto --es --common"}, "peerDependencies": {"vue": ">=3.0.3"}, "devDependencies": {"@types/lodash": "^4.14.165", "@moxo/tsconfig": "workspace:*", "@types/node": "^13.13.15", "lodash": "^4.17.15", "rimraf": "^3.0.2", "typescript": "^5.8.3"}, "dependencies": {"colors": "^1.4.0", "commander": "^13.1.0", "lodash": "^4.17.21", "ora": "^8.2.0", "protobufjs": "^7.5.0", "protobufjs-cli": "^1.1.3", "rimraf": "^6.0.1"}, "description": "protobuf types"}