import  fs from "fs";

import path from "path";
import {exec} from "child_process";

 export function resolve(fileName){
    const params = [process.cwd()].concat(Array.from(arguments))
    return path.resolve.apply(null, params);
}
 export function cleanDir(dirPath){
     fs.rmSync(dirPath, { recursive: true ,force: true});
     fs.mkdirSync(dirPath);
}
 export function writeFile(name, content){
    try {
        const dirPath = path.dirname(name);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
        fs.writeFileSync(name, content);
    }catch (e) {

    }
}
export function runCmd(cmd, arg ,spinner){
     return new Promise((resolve, reject) => {
         exec(cmd + ' ' + arg, (error, stdout, stderr) => {
             if (error) {
                 reject(error);
                 return;
             }
             resolve()
         });
    });

}
export async function copyDTSFiles(srcDir, destDir, excludeFiles = []) {
    await fs.mkdirSync(destDir, { recursive: true }); // 确保目标目录存在

    const entries = await fs.readdirSync(srcDir, { withFileTypes: true });

    for (const entry of entries) {
        const srcPath = path.join(srcDir, entry.name);
        const destPath = path.join(destDir, entry.name);

        if (entry.isDirectory()) {
            // 递归处理子目录
            await copyDTSFiles(srcPath, path.join(destDir, entry.name));
        } else if (entry.isFile() && entry.name.endsWith('.d.ts') && !excludeFiles.includes(entry.name)) {
            await fs.copyFileSync(srcPath, destPath);
        }
    }
}
export default {
     resolve, cleanDir, writeFile
}