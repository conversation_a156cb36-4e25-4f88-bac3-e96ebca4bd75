import { Command } from 'commander';
import {protoBuf2TS, protoBuf2TSJSON} from './transform.mjs'
import {copyDTSFiles,runCmd,resolve,cleanDir} from './utils.mjs'
const program = new Command();
import ora from "ora";

program
    .name('xproto')
    .description('CLI to generate typescript file from protobuf')
    .version('0.0.1');


program.command('build')
    .description('Split a string into substrings and display as an array')
    .option('-s, --source <string>', 'Protobuf file')
    .option('-o, --outDir <string>', 'out put dir', '.')
    .option('--es', 'export es modules')
    .option('--common', 'export common modules')
    .action(async (options) => {



        const spinner = ora(`Start converting the file [${options.source}]`);
        try {
            spinner.start('Converting ts')
            await protoBuf2TS(options.source, options.outDir)
            spinner.succeed()
            if (options.es) {
                spinner.start('Generate es module ...')
                await runCmd('tsc','--project tsconfig.build.json --module esnext --outDir es')
                spinner.succeed()
            }
            if (options.common) {
                spinner.start('Generate common module ...')
                await runCmd('tsc', ' --project tsconfig.build.json --module commonjs --outDir lib')
                spinner.succeed()
            }
            spinner.start('Build dts files ...')
            await copyDTSFiles('./es', './types', ['index.d.ts'])
            spinner.succeed()
            spinner.start('Build ProtoBufDefineJSON')
            await protoBuf2TSJSON(options.source, options.outDir, 'ProtoBufDefineJSON')
            spinner.succeed()
            spinner.start('Build static protobuf js ...')
            const outFile = resolve('./protobuf-static.js')
            await runCmd('npx', `pbjs --target static-module --wrap commonjs --no-verify --no-beautify --no-comments --keep-case --out ${outFile} ${resolve(options.source)}`)
            spinner.succeed()
            // spinner.start('Build protobuf types ...')
            // await runCmd('npx',`pbts --out index.d.ts  ${outFile}`)
            // spinner.succeed()
            cleanDir('./src')
        }catch (err){
            spinner.fail()
            console.error(err)
        }
        process.exit(0)
    });

program.parse();