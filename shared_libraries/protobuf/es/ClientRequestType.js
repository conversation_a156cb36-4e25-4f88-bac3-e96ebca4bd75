export var ClientRequestType;
(function (ClientRequestType) {
    ClientRequestType["INVALID_REQUEST"] = "INVALID_REQUEST";
    ClientRequestType["CLIENT_REQUEST_CONNECT"] = "CLIENT_REQUEST_CONNECT";
    ClientRequestType["JOB_REQUEST_CONNECT"] = "JOB_REQUEST_CONNECT";
    ClientRequestType["CLIENT_REQUEST_PING"] = "CLIENT_REQUEST_PING";
    ClientRequestType["USER_REQUEST_SSO"] = "USER_REQUEST_SSO";
    ClientRequestType["USER_REQUEST_READ_SSO_OPTIONS"] = "USER_REQUEST_READ_SSO_OPTIONS";
    ClientRequestType["USER_REQUEST_REGISTER"] = "USER_REQUEST_REGISTER";
    ClientRequestType["USER_REQUEST_REGISTER_AGENT"] = "USER_REQUEST_REGISTER_AGENT";
    ClientRequestType["USER_REQUEST_UNREGISTER_AGENT"] = "USER_REQUEST_UNREGISTER_AGENT";
    ClientRequestType["USER_REQUEST_RESEND_VERIFICATION_EMAIL"] = "USER_REQUEST_RESEND_VERIFICATION_EMAIL";
    ClientRequestType["USER_REQUEST_VERIFY_EMAIL_TOKEN"] = "USER_REQUEST_VERIFY_EMAIL_TOKEN";
    ClientRequestType["USER_REQUEST_ENTER_FOREGROUND"] = "USER_REQUEST_ENTER_FOREGROUND";
    ClientRequestType["USER_REQUEST_RESEND_VERIFICATION_CODE"] = "USER_REQUEST_RESEND_VERIFICATION_CODE";
    ClientRequestType["USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL"] = "USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL";
    ClientRequestType["USER_REQUEST_VERIFY_CODE"] = "USER_REQUEST_VERIFY_CODE";
    ClientRequestType["USER_REQUEST_VERIFY_EMAIL_CODE"] = "USER_REQUEST_VERIFY_EMAIL_CODE";
    ClientRequestType["USER_REQUEST_READ"] = "USER_REQUEST_READ";
    ClientRequestType["USER_REQUEST_READ_CAP"] = "USER_REQUEST_READ_CAP";
    ClientRequestType["USER_REQUEST_UNREAD_FEEDS_COUNT"] = "USER_REQUEST_UNREAD_FEEDS_COUNT";
    ClientRequestType["USER_REQUEST_SUBSCRIBE"] = "USER_REQUEST_SUBSCRIBE";
    ClientRequestType["USER_REQUEST_READ_FEEDS"] = "USER_REQUEST_READ_FEEDS";
    ClientRequestType["USER_REQUEST_READ_NOTES"] = "USER_REQUEST_READ_NOTES";
    ClientRequestType["USER_REQUEST_UPDATE"] = "USER_REQUEST_UPDATE";
    ClientRequestType["USER_REQUEST_UPDATE_PICTURES"] = "USER_REQUEST_UPDATE_PICTURES";
    ClientRequestType["USER_REQUEST_UPDATE_NAME"] = "USER_REQUEST_UPDATE_NAME";
    ClientRequestType["USER_REQUEST_UPDATE_PHONE_NUMBER"] = "USER_REQUEST_UPDATE_PHONE_NUMBER";
    ClientRequestType["USER_REQUEST_UPDATE_EMAIL"] = "USER_REQUEST_UPDATE_EMAIL";
    ClientRequestType["USER_REQUEST_UPDATE_USER_BOARD"] = "USER_REQUEST_UPDATE_USER_BOARD";
    ClientRequestType["USER_REQUEST_UPDATE_USER_BOARD_ENTER"] = "USER_REQUEST_UPDATE_USER_BOARD_ENTER";
    ClientRequestType["USER_REQUEST_UPDATE_USER_GROUP"] = "USER_REQUEST_UPDATE_USER_GROUP";
    ClientRequestType["USER_REQUEST_UPDATE_AGENT"] = "USER_REQUEST_UPDATE_AGENT";
    ClientRequestType["USER_REQUEST_LOGIN"] = "USER_REQUEST_LOGIN";
    ClientRequestType["USER_REQUEST_RESOURCE_TOKEN"] = "USER_REQUEST_RESOURCE_TOKEN";
    ClientRequestType["USER_REQUEST_ACCESS_TOKEN"] = "USER_REQUEST_ACCESS_TOKEN";
    ClientRequestType["USER_REQUEST_VERIFY_TOKEN"] = "USER_REQUEST_VERIFY_TOKEN";
    ClientRequestType["USER_REQUEST_DUPLICATE_TOKEN"] = "USER_REQUEST_DUPLICATE_TOKEN";
    ClientRequestType["USER_REQUEST_VERIFY_PASSWORD"] = "USER_REQUEST_VERIFY_PASSWORD";
    ClientRequestType["USER_REQUEST_REFRESH_TOKEN"] = "USER_REQUEST_REFRESH_TOKEN";
    ClientRequestType["USER_REQUEST_LOGOUT"] = "USER_REQUEST_LOGOUT";
    ClientRequestType["USER_REQUEST_LOGOUT_ALL_DEVICES"] = "USER_REQUEST_LOGOUT_ALL_DEVICES";
    ClientRequestType["USER_REQUEST_UPLOAD_RESOURCE"] = "USER_REQUEST_UPLOAD_RESOURCE";
    ClientRequestType["USER_REQUEST_UPLOAD_PROFILE_PICTURES"] = "USER_REQUEST_UPLOAD_PROFILE_PICTURES";
    ClientRequestType["USER_REQUEST_DOWNLOAD_RESOURCE"] = "USER_REQUEST_DOWNLOAD_RESOURCE";
    ClientRequestType["USER_REQUEST_RESET_PASSWORD"] = "USER_REQUEST_RESET_PASSWORD";
    ClientRequestType["USER_REQUEST_CHANGE_PASSWORD"] = "USER_REQUEST_CHANGE_PASSWORD";
    ClientRequestType["USER_REQUEST_CATEGORY_CREATE"] = "USER_REQUEST_CATEGORY_CREATE";
    ClientRequestType["USER_REQUEST_CATEGORY_RENAME"] = "USER_REQUEST_CATEGORY_RENAME";
    ClientRequestType["USER_REQUEST_CATEGORY_DELETE"] = "USER_REQUEST_CATEGORY_DELETE";
    ClientRequestType["USER_REQUEST_CATEGORY_ASSIGN"] = "USER_REQUEST_CATEGORY_ASSIGN";
    ClientRequestType["USER_REQUEST_READ_SESSIONS"] = "USER_REQUEST_READ_SESSIONS";
    ClientRequestType["USER_REQUEST_READ_BOARDS"] = "USER_REQUEST_READ_BOARDS";
    ClientRequestType["USER_REQUEST_READ_RELATIONS"] = "USER_REQUEST_READ_RELATIONS";
    ClientRequestType["USER_REQUEST_READ_AUTO_ARCHIVED_BOARDS"] = "USER_REQUEST_READ_AUTO_ARCHIVED_BOARDS";
    ClientRequestType["USER_REQUEST_READ_USER_BOARDS"] = "USER_REQUEST_READ_USER_BOARDS";
    ClientRequestType["USER_REQUEST_EMAIL_LOOKUP"] = "USER_REQUEST_EMAIL_LOOKUP";
    ClientRequestType["USER_REQUEST_PHONE_NUMBER_LOOKUP"] = "USER_REQUEST_PHONE_NUMBER_LOOKUP";
    ClientRequestType["USER_REQUEST_BOARD_LOOKUP"] = "USER_REQUEST_BOARD_LOOKUP";
    ClientRequestType["USER_REQUEST_RELATION_LOOKUP"] = "USER_REQUEST_RELATION_LOOKUP";
    ClientRequestType["USER_REQUEST_REMOVE_FAVORITE"] = "USER_REQUEST_REMOVE_FAVORITE";
    ClientRequestType["USER_REQUEST_REMOVE_MENTIONME"] = "USER_REQUEST_REMOVE_MENTIONME";
    ClientRequestType["USER_REQUEST_REMOVE_MENTIONME_BEFORE"] = "USER_REQUEST_REMOVE_MENTIONME_BEFORE";
    ClientRequestType["USER_REQUEST_REMOVE_NOTIFICATION"] = "USER_REQUEST_REMOVE_NOTIFICATION";
    ClientRequestType["USER_REQUEST_REMOVE_NOTIFICATION_BEFORE"] = "USER_REQUEST_REMOVE_NOTIFICATION_BEFORE";
    ClientRequestType["USER_REQUEST_UPDATE_ORDER_NUMBER"] = "USER_REQUEST_UPDATE_ORDER_NUMBER";
    ClientRequestType["USER_REQUEST_UPDATE_ACTION_ITEM"] = "USER_REQUEST_UPDATE_ACTION_ITEM";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER"] = "USER_REQUEST_REGISTER_LOCAL_USER";
    ClientRequestType["USER_REQUEST_LOGIN_LOCAL_USER"] = "USER_REQUEST_LOGIN_LOCAL_USER";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN"] = "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN";
    ClientRequestType["USER_REQUEST_VERIFY_LOCAL_EMAIL_CODE"] = "USER_REQUEST_VERIFY_LOCAL_EMAIL_CODE";
    ClientRequestType["USER_REQUEST_CREATE_RELATION_VIA_QR_TOKEN"] = "USER_REQUEST_CREATE_RELATION_VIA_QR_TOKEN";
    ClientRequestType["USER_REQUEST_PREVIEW_EMAIL_TOKEN"] = "USER_REQUEST_PREVIEW_EMAIL_TOKEN";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER_BY_APPLE_ID"] = "USER_REQUEST_REGISTER_LOCAL_USER_BY_APPLE_ID";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_APPLE_ID"] = "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_APPLE_ID";
    ClientRequestType["USER_REQUEST_LOGIN_LOCAL_USER_BY_APPLE_ID"] = "USER_REQUEST_LOGIN_LOCAL_USER_BY_APPLE_ID";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER_BY_GOOGLE_ID"] = "USER_REQUEST_REGISTER_LOCAL_USER_BY_GOOGLE_ID";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_GOOGLE_ID"] = "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_GOOGLE_ID";
    ClientRequestType["USER_REQUEST_LOGIN_LOCAL_USER_BY_GOOGLE_ID"] = "USER_REQUEST_LOGIN_LOCAL_USER_BY_GOOGLE_ID";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER_BY_VERIFICATION_CODE"] = "USER_REQUEST_REGISTER_LOCAL_USER_BY_VERIFICATION_CODE";
    ClientRequestType["USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE"] = "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE";
    ClientRequestType["USER_REQUEST_LOGIN_LOCAL_USER_BY_VERIFICATION_CODE"] = "USER_REQUEST_LOGIN_LOCAL_USER_BY_VERIFICATION_CODE";
    ClientRequestType["USER_REQUEST_LOGIN_LOCAL_USER_BY_GROUP_INVITATION_TOKEN"] = "USER_REQUEST_LOGIN_LOCAL_USER_BY_GROUP_INVITATION_TOKEN";
    ClientRequestType["USER_REQUEST_LOGIN_LOCAL_USER_BY_SALESFORCE_ID"] = "USER_REQUEST_LOGIN_LOCAL_USER_BY_SALESFORCE_ID";
    ClientRequestType["USER_REQUEST_UPDATE_DEVICE_TOKEN"] = "USER_REQUEST_UPDATE_DEVICE_TOKEN";
    ClientRequestType["USER_REQUEST_SEARCH"] = "USER_REQUEST_SEARCH";
    ClientRequestType["USER_REQUEST_SEARCH_CONTENT_LIBRARY_BOARD"] = "USER_REQUEST_SEARCH_CONTENT_LIBRARY_BOARD";
    ClientRequestType["USER_REQUEST_SEARCH_FLOW_TEMPLATE_LIBRARY_BOARD"] = "USER_REQUEST_SEARCH_FLOW_TEMPLATE_LIBRARY_BOARD";
    ClientRequestType["USER_REQUEST_KEEP_ALIVE"] = "USER_REQUEST_KEEP_ALIVE";
    ClientRequestType["USER_REQUEST_EMAIL_AUTH"] = "USER_REQUEST_EMAIL_AUTH";
    ClientRequestType["USER_REQUEST_EMAIL_DELIVERY"] = "USER_REQUEST_EMAIL_DELIVERY";
    ClientRequestType["USER_REQUEST_CONTACT_INVITE"] = "USER_REQUEST_CONTACT_INVITE";
    ClientRequestType["USER_REQUEST_CONTACT_ACCEPT"] = "USER_REQUEST_CONTACT_ACCEPT";
    ClientRequestType["USER_REQUEST_CONTACT_DENY"] = "USER_REQUEST_CONTACT_DENY";
    ClientRequestType["USER_REQUEST_CONTACT_CANCEL"] = "USER_REQUEST_CONTACT_CANCEL";
    ClientRequestType["USER_REQUEST_CONTACT_VIEW_INVITATION"] = "USER_REQUEST_CONTACT_VIEW_INVITATION";
    ClientRequestType["USER_REQUEST_CREATE_CONTACT"] = "USER_REQUEST_CREATE_CONTACT";
    ClientRequestType["USER_REQUEST_UPDATE_CONTACT"] = "USER_REQUEST_UPDATE_CONTACT";
    ClientRequestType["USER_REQUEST_DELETE_CONTACT"] = "USER_REQUEST_DELETE_CONTACT";
    ClientRequestType["USER_REQUEST_DELETE_RESOURCE"] = "USER_REQUEST_DELETE_RESOURCE";
    ClientRequestType["USER_REQUEST_FEEDBACK"] = "USER_REQUEST_FEEDBACK";
    ClientRequestType["USER_REQUEST_FEEDBACK_AND_ATTACHMENT"] = "USER_REQUEST_FEEDBACK_AND_ATTACHMENT";
    ClientRequestType["USER_REQUEST_SYSTEM_FEEDBACK"] = "USER_REQUEST_SYSTEM_FEEDBACK";
    ClientRequestType["USER_REQUEST_SSO_REGISTERED_USER"] = "USER_REQUEST_SSO_REGISTERED_USER";
    ClientRequestType["USER_REQUEST_SSO_GROUP_UNIQUE_ID"] = "USER_REQUEST_SSO_GROUP_UNIQUE_ID";
    ClientRequestType["USER_REQUEST_SSO_EXTERNAL_MOXTRA"] = "USER_REQUEST_SSO_EXTERNAL_MOXTRA";
    ClientRequestType["USER_REQUEST_SSO_REDIRECT"] = "USER_REQUEST_SSO_REDIRECT";
    ClientRequestType["USER_REQUEST_UPDATE_SIP_REGISTRATION_STATUS"] = "USER_REQUEST_UPDATE_SIP_REGISTRATION_STATUS";
    ClientRequestType["USER_REQUEST_CREATE_CALL_LOG"] = "USER_REQUEST_CREATE_CALL_LOG";
    ClientRequestType["USER_REQUEST_UPDATE_CALL_LOG"] = "USER_REQUEST_UPDATE_CALL_LOG";
    ClientRequestType["USER_REQUEST_DELETE_CALL_LOG"] = "USER_REQUEST_DELETE_CALL_LOG";
    ClientRequestType["USER_REQUEST_READ_PASSWORD_RULE"] = "USER_REQUEST_READ_PASSWORD_RULE";
    ClientRequestType["USER_REQUEST_RESEND_DELETION_EMAIL"] = "USER_REQUEST_RESEND_DELETION_EMAIL";
    ClientRequestType["USER_REQUEST_VERIFY_DELETION_TOKEN"] = "USER_REQUEST_VERIFY_DELETION_TOKEN";
    ClientRequestType["USER_REQUEST_DELETE"] = "USER_REQUEST_DELETE";
    ClientRequestType["USER_REQUEST_DELETE_LOCAL_USER"] = "USER_REQUEST_DELETE_LOCAL_USER";
    ClientRequestType["USER_REQUEST_FOLLOW_GROUP_BOARD"] = "USER_REQUEST_FOLLOW_GROUP_BOARD";
    ClientRequestType["USER_REQUEST_UNFOLLOW_GROUP_BOARD"] = "USER_REQUEST_UNFOLLOW_GROUP_BOARD";
    ClientRequestType["USER_REQUEST_UPDATE_GROUP_BOARD"] = "USER_REQUEST_UPDATE_GROUP_BOARD";
    ClientRequestType["USER_REQUEST_UPDATE_OUT_OF_OFFICE"] = "USER_REQUEST_UPDATE_OUT_OF_OFFICE";
    ClientRequestType["USER_REQUEST_POST_ACTIVITY"] = "USER_REQUEST_POST_ACTIVITY";
    ClientRequestType["USER_REQUEST_READ_ACTIVITY"] = "USER_REQUEST_READ_ACTIVITY";
    ClientRequestType["USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_EMAIL"] = "USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_EMAIL";
    ClientRequestType["USER_REQUEST_QR_TOKEN"] = "USER_REQUEST_QR_TOKEN";
    ClientRequestType["USER_REQUEST_VIEW_QR_TOKEN"] = "USER_REQUEST_VIEW_QR_TOKEN";
    ClientRequestType["USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_SMS"] = "USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_SMS";
    ClientRequestType["USER_REQUEST_VERIFY_LOCAL_SMS_CODE"] = "USER_REQUEST_VERIFY_LOCAL_SMS_CODE";
    ClientRequestType["USER_REQUEST_VERIFY_LOCAL_APPLE_JWT"] = "USER_REQUEST_VERIFY_LOCAL_APPLE_JWT";
    ClientRequestType["USER_REQUEST_VERIFY_LOCAL_GOOGLE_JWT"] = "USER_REQUEST_VERIFY_LOCAL_GOOGLE_JWT";
    ClientRequestType["USER_REQUEST_RESEND_APP_DOWNLOAD_LINK_SMS"] = "USER_REQUEST_RESEND_APP_DOWNLOAD_LINK_SMS";
    ClientRequestType["USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_EMAIL"] = "USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_EMAIL";
    ClientRequestType["USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_PHONE_NUMBER"] = "USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_PHONE_NUMBER";
    ClientRequestType["USER_REQUEST_GLOBAL_RESEND_SMS_CODE"] = "USER_REQUEST_GLOBAL_RESEND_SMS_CODE";
    ClientRequestType["USER_REQUEST_GLOBAL_VERIFY_SMS_CODE"] = "USER_REQUEST_GLOBAL_VERIFY_SMS_CODE";
    ClientRequestType["USER_REQUEST_GLOBAL_RESEND_EMAIL_CODE"] = "USER_REQUEST_GLOBAL_RESEND_EMAIL_CODE";
    ClientRequestType["USER_REQUEST_GLOBAL_VERIFY_EMAIL_CODE"] = "USER_REQUEST_GLOBAL_VERIFY_EMAIL_CODE";
    ClientRequestType["USER_REQUEST_PUSH_NOTIFICATION"] = "USER_REQUEST_PUSH_NOTIFICATION";
    ClientRequestType["USER_REQUEST_SMS"] = "USER_REQUEST_SMS";
    ClientRequestType["USER_REQUEST_CREATE_BROADCAST"] = "USER_REQUEST_CREATE_BROADCAST";
    ClientRequestType["USER_REQUEST_UPDATE_BROADCAST"] = "USER_REQUEST_UPDATE_BROADCAST";
    ClientRequestType["USER_REQUEST_DELETE_BROADCAST"] = "USER_REQUEST_DELETE_BROADCAST";
    ClientRequestType["USER_REQUEST_GET_HTML"] = "USER_REQUEST_GET_HTML";
    ClientRequestType["USER_REQUEST_SHORT_URL"] = "USER_REQUEST_SHORT_URL";
    ClientRequestType["USER_REQUEST_LOOKUP_SHORT_URL"] = "USER_REQUEST_LOOKUP_SHORT_URL";
    ClientRequestType["USER_REQUEST_VERIFY_GOOGLE_PLAY_INTEGRITY_TOKEN"] = "USER_REQUEST_VERIFY_GOOGLE_PLAY_INTEGRITY_TOKEN";
    ClientRequestType["USER_REQUEST_USER_BOARD_ARCHIVE"] = "USER_REQUEST_USER_BOARD_ARCHIVE";
    ClientRequestType["USER_REQUEST_MAX"] = "USER_REQUEST_MAX";
    ClientRequestType["BOARD_REQUEST_CREATE"] = "BOARD_REQUEST_CREATE";
    ClientRequestType["BOARD_REQUEST_DUPLICATE"] = "BOARD_REQUEST_DUPLICATE";
    ClientRequestType["BOARD_REQUEST_READ"] = "BOARD_REQUEST_READ";
    ClientRequestType["BOARD_REQUEST_VIEW"] = "BOARD_REQUEST_VIEW";
    ClientRequestType["BOARD_REQUEST_VIEW_AS_OGO"] = "BOARD_REQUEST_VIEW_AS_OGO";
    ClientRequestType["BOARD_REQUEST_READ_FEEDS"] = "BOARD_REQUEST_READ_FEEDS";
    ClientRequestType["BOARD_REQUEST_SUBSCRIBE"] = "BOARD_REQUEST_SUBSCRIBE";
    ClientRequestType["BOARD_REQUEST_UNSUBSCRIBE"] = "BOARD_REQUEST_UNSUBSCRIBE";
    ClientRequestType["BOARD_REQUEST_SUBSCRIBE_MULTIPLE"] = "BOARD_REQUEST_SUBSCRIBE_MULTIPLE";
    ClientRequestType["BOARD_REQUEST_UPDATE"] = "BOARD_REQUEST_UPDATE";
    ClientRequestType["BOARD_REQUEST_COPY_PAGES"] = "BOARD_REQUEST_COPY_PAGES";
    ClientRequestType["BOARD_REQUEST_COPY_RESOURCES"] = "BOARD_REQUEST_COPY_RESOURCES";
    ClientRequestType["BOARD_REQUEST_COPY_TODOS"] = "BOARD_REQUEST_COPY_TODOS";
    ClientRequestType["BOARD_REQUEST_CREATE_COMMENT"] = "BOARD_REQUEST_CREATE_COMMENT";
    ClientRequestType["BOARD_REQUEST_UPLOAD_COMMENT"] = "BOARD_REQUEST_UPLOAD_COMMENT";
    ClientRequestType["BOARD_REQUEST_UPDATE_COMMENT"] = "BOARD_REQUEST_UPDATE_COMMENT";
    ClientRequestType["BOARD_REQUEST_DELETE_COMMENT"] = "BOARD_REQUEST_DELETE_COMMENT";
    ClientRequestType["BOARD_REQUEST_TYPE_INDICATION"] = "BOARD_REQUEST_TYPE_INDICATION";
    ClientRequestType["BOARD_REQUEST_DELETE"] = "BOARD_REQUEST_DELETE";
    ClientRequestType["BOARD_REQUEST_UPDATE_COMMENT_URL_PREVIEW"] = "BOARD_REQUEST_UPDATE_COMMENT_URL_PREVIEW";
    ClientRequestType["BOARD_REQUEST_DELETE_COMMENT_URL_PREVIEW"] = "BOARD_REQUEST_DELETE_COMMENT_URL_PREVIEW";
    ClientRequestType["BOARD_REQUEST_INCREASE_USED_COUNT"] = "BOARD_REQUEST_INCREASE_USED_COUNT";
    ClientRequestType["BOARD_REQUEST_UPDATE_BOARD_USER"] = "BOARD_REQUEST_UPDATE_BOARD_USER";
    ClientRequestType["BOARD_REQUEST_UPLOAD_RESOURCE"] = "BOARD_REQUEST_UPLOAD_RESOURCE";
    ClientRequestType["BOARD_REQUEST_UPLOAD_AUDIO"] = "BOARD_REQUEST_UPLOAD_AUDIO";
    ClientRequestType["BOARD_REQUEST_DOWNLOAD_RESOURCE"] = "BOARD_REQUEST_DOWNLOAD_RESOURCE";
    ClientRequestType["BOARD_REQUEST_DOWNLOAD_BOARD"] = "BOARD_REQUEST_DOWNLOAD_BOARD";
    ClientRequestType["BOARD_REQUEST_UPLOAD_RESOURCE_URL"] = "BOARD_REQUEST_UPLOAD_RESOURCE_URL";
    ClientRequestType["BOARD_REQUEST_DOWNLOAD_FOLDER"] = "BOARD_REQUEST_DOWNLOAD_FOLDER";
    ClientRequestType["BOARD_REQUEST_DOWNLOAD_ZIP"] = "BOARD_REQUEST_DOWNLOAD_ZIP";
    ClientRequestType["BOARD_REQUEST_INVITE"] = "BOARD_REQUEST_INVITE";
    ClientRequestType["BOARD_REQUEST_JOIN"] = "BOARD_REQUEST_JOIN";
    ClientRequestType["BOARD_REQUEST_LEAVE"] = "BOARD_REQUEST_LEAVE";
    ClientRequestType["BOARD_REQUEST_APPROVE"] = "BOARD_REQUEST_APPROVE";
    ClientRequestType["BOARD_REQUEST_DENY"] = "BOARD_REQUEST_DENY";
    ClientRequestType["BOARD_REQUEST_EXPEL"] = "BOARD_REQUEST_EXPEL";
    ClientRequestType["BOARD_REQUEST_SET_ACCESS_TYPE"] = "BOARD_REQUEST_SET_ACCESS_TYPE";
    ClientRequestType["BOARD_REQUEST_VIEW_INVITATION"] = "BOARD_REQUEST_VIEW_INVITATION";
    ClientRequestType["BOARD_REQUEST_INVITE_OUT_OF_OFFICE_BACKUP_USER"] = "BOARD_REQUEST_INVITE_OUT_OF_OFFICE_BACKUP_USER";
    ClientRequestType["BOARD_REQUEST_ACTION_TRANSFER"] = "BOARD_REQUEST_ACTION_TRANSFER";
    ClientRequestType["BOARD_REQUEST_GET_RECORDINGS"] = "BOARD_REQUEST_GET_RECORDINGS";
    ClientRequestType["BOARD_REQUEST_JOIN_BY_VIEW_TOKEN"] = "BOARD_REQUEST_JOIN_BY_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_UPDATE_REQUESTING_USER"] = "BOARD_REQUEST_UPDATE_REQUESTING_USER";
    ClientRequestType["BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_APPLE_ID"] = "BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_APPLE_ID";
    ClientRequestType["BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_GOOGLE_ID"] = "BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_GOOGLE_ID";
    ClientRequestType["BOARD_REQUEST_CREATE_VIEW_TOKEN"] = "BOARD_REQUEST_CREATE_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_UPDATE_VIEW_TOKEN"] = "BOARD_REQUEST_UPDATE_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_REMOVE_VIEW_TOKEN"] = "BOARD_REQUEST_REMOVE_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_EMAIL_VIEW_TOKEN"] = "BOARD_REQUEST_EMAIL_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_READ_VIEW_TOKEN"] = "BOARD_REQUEST_READ_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_READ_VIEW_TOKENS"] = "BOARD_REQUEST_READ_VIEW_TOKENS";
    ClientRequestType["BOARD_REQUEST_SEARCH_BOARD"] = "BOARD_REQUEST_SEARCH_BOARD";
    ClientRequestType["BOARD_REQUEST_SEARCH_GROUP_BOARD"] = "BOARD_REQUEST_SEARCH_GROUP_BOARD";
    ClientRequestType["BOARD_REQUEST_SEARCH_GROUP"] = "BOARD_REQUEST_SEARCH_GROUP";
    ClientRequestType["SESSION_REQUEST_START"] = "SESSION_REQUEST_START";
    ClientRequestType["SESSION_REQUEST_RESTART"] = "SESSION_REQUEST_RESTART";
    ClientRequestType["SESSION_REQUEST_END"] = "SESSION_REQUEST_END";
    ClientRequestType["SESSION_REQUEST_JOIN"] = "SESSION_REQUEST_JOIN";
    ClientRequestType["SESSION_REQUEST_SUBSCRIBE"] = "SESSION_REQUEST_SUBSCRIBE";
    ClientRequestType["SESSION_REQUEST_LEAVE"] = "SESSION_REQUEST_LEAVE";
    ClientRequestType["SESSION_REQUEST_INVITE"] = "SESSION_REQUEST_INVITE";
    ClientRequestType["SESSION_REQUEST_KEEP_ALIVE"] = "SESSION_REQUEST_KEEP_ALIVE";
    ClientRequestType["SESSION_REQUEST_EVENT_LOG"] = "SESSION_REQUEST_EVENT_LOG";
    ClientRequestType["SESSION_REQUEST_READ_EVENT_LOG"] = "SESSION_REQUEST_READ_EVENT_LOG";
    ClientRequestType["SESSION_REQUEST_WEBRTC_OFFER"] = "SESSION_REQUEST_WEBRTC_OFFER";
    ClientRequestType["SESSION_REQUEST_CHANGE_PRESENTER"] = "SESSION_REQUEST_CHANGE_PRESENTER";
    ClientRequestType["SESSION_REQUEST_START_DS"] = "SESSION_REQUEST_START_DS";
    ClientRequestType["SESSION_REQUEST_STOP_DS"] = "SESSION_REQUEST_STOP_DS";
    ClientRequestType["SESSION_REQUEST_PUBLISH_DS_STATE"] = "SESSION_REQUEST_PUBLISH_DS_STATE";
    ClientRequestType["SESSION_REQUEST_PUBLISH_AUDIO_STATE"] = "SESSION_REQUEST_PUBLISH_AUDIO_STATE";
    ClientRequestType["SESSION_REQUEST_PUBLISH_VIDEO_STATE"] = "SESSION_REQUEST_PUBLISH_VIDEO_STATE";
    ClientRequestType["SESSION_REQUEST_READ"] = "SESSION_REQUEST_READ";
    ClientRequestType["SESSION_REQUEST_READ_ROSTER"] = "SESSION_REQUEST_READ_ROSTER";
    ClientRequestType["SESSION_REQUEST_UPDATE_AUDIO_STATUS"] = "SESSION_REQUEST_UPDATE_AUDIO_STATUS";
    ClientRequestType["SESSION_REQUEST_UPDATE_VIDEO_STATUS"] = "SESSION_REQUEST_UPDATE_VIDEO_STATUS";
    ClientRequestType["SESSION_REQUEST_START_SESSION"] = "SESSION_REQUEST_START_SESSION";
    ClientRequestType["SESSION_REQUEST_RECLAIM_HOST"] = "SESSION_REQUEST_RECLAIM_HOST";
    ClientRequestType["SESSION_REQUEST_SET_PRESENTER"] = "SESSION_REQUEST_SET_PRESENTER";
    ClientRequestType["SESSION_REQUEST_SET_HOST"] = "SESSION_REQUEST_SET_HOST";
    ClientRequestType["SESSION_REQUEST_MUTE"] = "SESSION_REQUEST_MUTE";
    ClientRequestType["SESSION_REQUEST_UNMUTE"] = "SESSION_REQUEST_UNMUTE";
    ClientRequestType["SESSION_REQUEST_MUTE_ALL"] = "SESSION_REQUEST_MUTE_ALL";
    ClientRequestType["SESSION_REQUEST_SWITCH_PAGE"] = "SESSION_REQUEST_SWITCH_PAGE";
    ClientRequestType["SESSION_REQUEST_START_DESKTOPSHARE"] = "SESSION_REQUEST_START_DESKTOPSHARE";
    ClientRequestType["SESSION_REQUEST_LEAVE_TELEPHONY"] = "SESSION_REQUEST_LEAVE_TELEPHONY";
    ClientRequestType["SESSION_REQUEST_SCHEDULE"] = "SESSION_REQUEST_SCHEDULE";
    ClientRequestType["SESSION_REQUEST_DOWNLOAD_CALENDAR"] = "SESSION_REQUEST_DOWNLOAD_CALENDAR";
    ClientRequestType["SESSION_REQUEST_CREATE_PERSONAL_ROOM"] = "SESSION_REQUEST_CREATE_PERSONAL_ROOM";
    ClientRequestType["SESSION_REQUEST_REMOVE_ROSTER"] = "SESSION_REQUEST_REMOVE_ROSTER";
    ClientRequestType["SESSION_REQUEST_LOCK"] = "SESSION_REQUEST_LOCK";
    ClientRequestType["SESSION_REQUEST_UNLOCK"] = "SESSION_REQUEST_UNLOCK";
    ClientRequestType["SESSION_REQUEST_SCHEDULE_TO_BOARD_USER"] = "SESSION_REQUEST_SCHEDULE_TO_BOARD_USER";
    ClientRequestType["BOARD_REQUEST_UPDATE_BOARD"] = "BOARD_REQUEST_UPDATE_BOARD";
    ClientRequestType["BOARD_REQUEST_UPLOAD_BOARD_RESOURCE"] = "BOARD_REQUEST_UPLOAD_BOARD_RESOURCE";
    ClientRequestType["BOARD_REQUEST_CREATE_PAGE"] = "BOARD_REQUEST_CREATE_PAGE";
    ClientRequestType["BOARD_REQUEST_UPLOAD_PAGE"] = "BOARD_REQUEST_UPLOAD_PAGE";
    ClientRequestType["BOARD_REQUEST_UPDATE_PAGE"] = "BOARD_REQUEST_UPDATE_PAGE";
    ClientRequestType["BOARD_REQUEST_DELETE_PAGE"] = "BOARD_REQUEST_DELETE_PAGE";
    ClientRequestType["BOARD_REQUEST_DELETE_RESOURCE"] = "BOARD_REQUEST_DELETE_RESOURCE";
    ClientRequestType["BOARD_REQUEST_SET_EDITOR"] = "BOARD_REQUEST_SET_EDITOR";
    ClientRequestType["BOARD_REQUEST_REMOVE_EDITOR"] = "BOARD_REQUEST_REMOVE_EDITOR";
    ClientRequestType["BOARD_REQUEST_SET_EDITOR_TYPE"] = "BOARD_REQUEST_SET_EDITOR_TYPE";
    ClientRequestType["BOARD_REQUEST_CREATE_PAGE_ELEMENT"] = "BOARD_REQUEST_CREATE_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_UPLOAD_PAGE_ELEMENT"] = "BOARD_REQUEST_UPLOAD_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_UPDATE_PAGE_ELEMENT"] = "BOARD_REQUEST_UPDATE_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_DELETE_PAGE_ELEMENT"] = "BOARD_REQUEST_DELETE_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_CREATE_PAGE_COMMENT"] = "BOARD_REQUEST_CREATE_PAGE_COMMENT";
    ClientRequestType["BOARD_REQUEST_UPLOAD_PAGE_COMMENT"] = "BOARD_REQUEST_UPLOAD_PAGE_COMMENT";
    ClientRequestType["BOARD_REQUEST_UPDATE_PAGE_COMMENT"] = "BOARD_REQUEST_UPDATE_PAGE_COMMENT";
    ClientRequestType["BOARD_REQUEST_DELETE_PAGE_COMMENT"] = "BOARD_REQUEST_DELETE_PAGE_COMMENT";
    ClientRequestType["BOARD_REQUEST_CREATE_PAGE_POSITION_COMMENT"] = "BOARD_REQUEST_CREATE_PAGE_POSITION_COMMENT";
    ClientRequestType["BOARD_REQUEST_UPLOAD_PAGE_POSITION_COMMENT"] = "BOARD_REQUEST_UPLOAD_PAGE_POSITION_COMMENT";
    ClientRequestType["BOARD_REQUEST_UPDATE_PAGE_POSITION_COMMENT"] = "BOARD_REQUEST_UPDATE_PAGE_POSITION_COMMENT";
    ClientRequestType["BOARD_REQUEST_DELETE_PAGE_POSITION_COMMENT"] = "BOARD_REQUEST_DELETE_PAGE_POSITION_COMMENT";
    ClientRequestType["BOARD_REQUEST_CREATE_PAGE_GROUP"] = "BOARD_REQUEST_CREATE_PAGE_GROUP";
    ClientRequestType["BOARD_REQUEST_UPDATE_PAGE_GROUP"] = "BOARD_REQUEST_UPDATE_PAGE_GROUP";
    ClientRequestType["BOARD_REQUEST_DELETE_PAGE_GROUP"] = "BOARD_REQUEST_DELETE_PAGE_GROUP";
    ClientRequestType["BOARD_REQUEST_COPY_PAGE_GROUP"] = "BOARD_REQUEST_COPY_PAGE_GROUP";
    ClientRequestType["BOARD_REQUEST_MOVE_PAGE_GROUP"] = "BOARD_REQUEST_MOVE_PAGE_GROUP";
    ClientRequestType["BOARD_REQUEST_COPY_SIGNATURE"] = "BOARD_REQUEST_COPY_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_CREATE_PAGE_TAG"] = "BOARD_REQUEST_CREATE_PAGE_TAG";
    ClientRequestType["BOARD_REQUEST_UPDATE_PAGE_TAG"] = "BOARD_REQUEST_UPDATE_PAGE_TAG";
    ClientRequestType["BOARD_REQUEST_DELETE_PAGE_TAG"] = "BOARD_REQUEST_DELETE_PAGE_TAG";
    ClientRequestType["BOARD_REQUEST_DOWNLOAD_NOTE"] = "BOARD_REQUEST_DOWNLOAD_NOTE";
    ClientRequestType["BOARD_REQUEST_CREATE_TODO"] = "BOARD_REQUEST_CREATE_TODO";
    ClientRequestType["BOARD_REQUEST_UPLOAD_TODO"] = "BOARD_REQUEST_UPLOAD_TODO";
    ClientRequestType["BOARD_REQUEST_UPDATE_TODO"] = "BOARD_REQUEST_UPDATE_TODO";
    ClientRequestType["BOARD_REQUEST_DELETE_TODO"] = "BOARD_REQUEST_DELETE_TODO";
    ClientRequestType["BOARD_REQUEST_SET_TODO_ASSIGNEE"] = "BOARD_REQUEST_SET_TODO_ASSIGNEE";
    ClientRequestType["BOARD_REQUEST_SET_TODO_DUE_DATE"] = "BOARD_REQUEST_SET_TODO_DUE_DATE";
    ClientRequestType["BOARD_REQUEST_SET_TODO_COMPLETED"] = "BOARD_REQUEST_SET_TODO_COMPLETED";
    ClientRequestType["BOARD_REQUEST_UPDATE_TODO_ATTACHMENT"] = "BOARD_REQUEST_UPDATE_TODO_ATTACHMENT";
    ClientRequestType["BOARD_REQUEST_UPLOAD_TODO_COMMENT"] = "BOARD_REQUEST_UPLOAD_TODO_COMMENT";
    ClientRequestType["BOARD_REQUEST_CREATE_TODO_COMMENT"] = "BOARD_REQUEST_CREATE_TODO_COMMENT";
    ClientRequestType["BOARD_REQUEST_UPDATE_TODO_COMMENT"] = "BOARD_REQUEST_UPDATE_TODO_COMMENT";
    ClientRequestType["BOARD_REQUEST_DELETE_TODO_COMMENT"] = "BOARD_REQUEST_DELETE_TODO_COMMENT";
    ClientRequestType["BOARD_REQUEST_CREATE_TODO_REMINDER"] = "BOARD_REQUEST_CREATE_TODO_REMINDER";
    ClientRequestType["BOARD_REQUEST_UPDATE_TODO_REMINDER"] = "BOARD_REQUEST_UPDATE_TODO_REMINDER";
    ClientRequestType["BOARD_REQUEST_DELETE_TODO_REMINDER"] = "BOARD_REQUEST_DELETE_TODO_REMINDER";
    ClientRequestType["BOARD_REQUEST_DELETE_TODO_FILE"] = "BOARD_REQUEST_DELETE_TODO_FILE";
    ClientRequestType["BOARD_REQUEST_READ_FLAT_FEEDS"] = "BOARD_REQUEST_READ_FLAT_FEEDS";
    ClientRequestType["BOARD_REQUEST_READ_THREAD"] = "BOARD_REQUEST_READ_THREAD";
    ClientRequestType["BOARD_REQUEST_READ_ONGOING_SIGNATURES"] = "BOARD_REQUEST_READ_ONGOING_SIGNATURES";
    ClientRequestType["BOARD_REQUEST_READ_ONGOING_TRANSACTIONS"] = "BOARD_REQUEST_READ_ONGOING_TRANSACTIONS";
    ClientRequestType["BOARD_REQUEST_READ_ONGOING_DELEGATE_FEEDS"] = "BOARD_REQUEST_READ_ONGOING_DELEGATE_FEEDS";
    ClientRequestType["BOARD_REQUEST_READ_COVER"] = "BOARD_REQUEST_READ_COVER";
    ClientRequestType["BOARD_REQUEST_LIST_FOLDER"] = "BOARD_REQUEST_LIST_FOLDER";
    ClientRequestType["BOARD_REQUEST_READ_FILE"] = "BOARD_REQUEST_READ_FILE";
    ClientRequestType["BOARD_REQUEST_LIST_SIGNATURES"] = "BOARD_REQUEST_LIST_SIGNATURES";
    ClientRequestType["BOARD_REQUEST_LIST_TODOS"] = "BOARD_REQUEST_LIST_TODOS";
    ClientRequestType["BOARD_REQUEST_READ_SIGNATURE"] = "BOARD_REQUEST_READ_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_CREATE_REMINDER"] = "BOARD_REQUEST_CREATE_REMINDER";
    ClientRequestType["BOARD_REQUEST_UPDATE_REMINDER"] = "BOARD_REQUEST_UPDATE_REMINDER";
    ClientRequestType["BOARD_REQUEST_DELETE_REMINDER"] = "BOARD_REQUEST_DELETE_REMINDER";
    ClientRequestType["BOARD_REQUEST_READ_AUDIT_OBJECT"] = "BOARD_REQUEST_READ_AUDIT_OBJECT";
    ClientRequestType["BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_RESOURCE"] = "BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_RESOURCE";
    ClientRequestType["BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_BOARD"] = "BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_BOARD";
    ClientRequestType["BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_ZIP"] = "BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_ZIP";
    ClientRequestType["BOARD_REQUEST_RESEND_INVITATION_EMAIL"] = "BOARD_REQUEST_RESEND_INVITATION_EMAIL";
    ClientRequestType["BOARD_REQUEST_RESEND_INVITATION_SMS"] = "BOARD_REQUEST_RESEND_INVITATION_SMS";
    ClientRequestType["BOARD_REQUEST_CREATE_INVITATION_VIEW_TOKEN"] = "BOARD_REQUEST_CREATE_INVITATION_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_RESEND_VIEW_TOKEN"] = "BOARD_REQUEST_RESEND_VIEW_TOKEN";
    ClientRequestType["BOARD_REQUEST_RENEW_WORKSPACE_ID"] = "BOARD_REQUEST_RENEW_WORKSPACE_ID";
    ClientRequestType["BOARD_REQUEST_RESET_INVITE_CODE"] = "BOARD_REQUEST_RESET_INVITE_CODE";
    ClientRequestType["BOARD_PUBLISH_ACTION"] = "BOARD_PUBLISH_ACTION";
    ClientRequestType["OBSOLETE_BOARD_REQUEST_SET_EMAIL_ADDRESS"] = "OBSOLETE_BOARD_REQUEST_SET_EMAIL_ADDRESS";
    ClientRequestType["BOARD_REQUEST_SET_PHONE_NUMBER"] = "BOARD_REQUEST_SET_PHONE_NUMBER";
    ClientRequestType["BOARD_REQUEST_CREATE_FOLDER"] = "BOARD_REQUEST_CREATE_FOLDER";
    ClientRequestType["BOARD_REQUEST_UPDATE_FOLDER"] = "BOARD_REQUEST_UPDATE_FOLDER";
    ClientRequestType["BOARD_REQUEST_DELETE_FOLDER"] = "BOARD_REQUEST_DELETE_FOLDER";
    ClientRequestType["BOARD_REQUEST_COPY_FOLDER"] = "BOARD_REQUEST_COPY_FOLDER";
    ClientRequestType["BOARD_REQUEST_MOVE_FOLDER"] = "BOARD_REQUEST_MOVE_FOLDER";
    ClientRequestType["BOARD_REQUEST_CREATE_FAVORITE"] = "BOARD_REQUEST_CREATE_FAVORITE";
    ClientRequestType["BOARD_REQUEST_PIN"] = "BOARD_REQUEST_PIN";
    ClientRequestType["BOARD_REQUEST_CHECK_ISRESTRICT"] = "BOARD_REQUEST_CHECK_ISRESTRICT";
    ClientRequestType["BOARD_REQUEST_CALL_LOG"] = "BOARD_REQUEST_CALL_LOG";
    ClientRequestType["BOARD_REQUEST_SET_OWNER_DELEGATE"] = "BOARD_REQUEST_SET_OWNER_DELEGATE";
    ClientRequestType["BOARD_REQUEST_SET_FEED_STATUS"] = "BOARD_REQUEST_SET_FEED_STATUS";
    ClientRequestType["BOARD_REQUEST_SET_OWNER"] = "BOARD_REQUEST_SET_OWNER";
    ClientRequestType["BOARD_REQUEST_CREATE_SIGNATURE"] = "BOARD_REQUEST_CREATE_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_UPDATE_SIGNATURE"] = "BOARD_REQUEST_UPDATE_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_DELETE_SIGNATURE"] = "BOARD_REQUEST_DELETE_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_ADD_SIGNATURE_SIGNEE"] = "BOARD_REQUEST_ADD_SIGNATURE_SIGNEE";
    ClientRequestType["BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE"] = "BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE";
    ClientRequestType["BOARD_REQUEST_REMOVE_SIGNATURE_SIGNEE"] = "BOARD_REQUEST_REMOVE_SIGNATURE_SIGNEE";
    ClientRequestType["BOARD_REQUEST_CREATE_SIGNATURE_PAGE_ELEMENT"] = "BOARD_REQUEST_CREATE_SIGNATURE_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_UPLOAD_SIGNATURE_PAGE_ELEMENT"] = "BOARD_REQUEST_UPLOAD_SIGNATURE_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT"] = "BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_DELETE_SIGNATURE_PAGE_ELEMENT"] = "BOARD_REQUEST_DELETE_SIGNATURE_PAGE_ELEMENT";
    ClientRequestType["BOARD_REQUEST_START_SIGNATURE"] = "BOARD_REQUEST_START_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_UPLOAD_SIGNATURE_RESOURCE"] = "BOARD_REQUEST_UPLOAD_SIGNATURE_RESOURCE";
    ClientRequestType["BOARD_REQUEST_SUBMIT_SIGNATURE"] = "BOARD_REQUEST_SUBMIT_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_VIEW_SIGNATURE"] = "BOARD_REQUEST_VIEW_SIGNATURE";
    ClientRequestType["BOARD_REQUEST_SIGNEE_UPDATE"] = "BOARD_REQUEST_SIGNEE_UPDATE";
    ClientRequestType["BOARD_REQUEST_SIGNEE_UPLOAD_RESOURCE"] = "BOARD_REQUEST_SIGNEE_UPLOAD_RESOURCE";
    ClientRequestType["BOARD_REQUEST_DOWNLOAD_SIGNATURE_RESOURCE"] = "BOARD_REQUEST_DOWNLOAD_SIGNATURE_RESOURCE";
    ClientRequestType["BOARD_REQUEST_SIGNATURE_UPDATE_ATTACHMENT"] = "BOARD_REQUEST_SIGNATURE_UPDATE_ATTACHMENT";
    ClientRequestType["BOARD_REQUEST_SIGNATURE_DELETE_ATTACHMENT"] = "BOARD_REQUEST_SIGNATURE_DELETE_ATTACHMENT";
    ClientRequestType["BOARD_REQUEST_SIGNATURE_REOPEN"] = "BOARD_REQUEST_SIGNATURE_REOPEN";
    ClientRequestType["BOARD_REQUEST_SIGNATURE_RESET_STATUS"] = "BOARD_REQUEST_SIGNATURE_RESET_STATUS";
    ClientRequestType["BOARD_REQUEST_SIGNATURE_REPLACE"] = "BOARD_REQUEST_SIGNATURE_REPLACE";
    ClientRequestType["BOARD_REQUEST_SIGNATURE_REPLACE_PAGES_FROM_FILE"] = "BOARD_REQUEST_SIGNATURE_REPLACE_PAGES_FROM_FILE";
    ClientRequestType["BOARD_REQUEST_CREATE_WEBAPP_TOKEN"] = "BOARD_REQUEST_CREATE_WEBAPP_TOKEN";
    ClientRequestType["BOARD_REQUEST_WEBAPP_CALLBACK"] = "BOARD_REQUEST_WEBAPP_CALLBACK";
    ClientRequestType["FILE_FLOW_REQUEST_COMMENT_CREATE"] = "FILE_FLOW_REQUEST_COMMENT_CREATE";
    ClientRequestType["FILE_FLOW_REQUEST_COMMENT_UPDATE"] = "FILE_FLOW_REQUEST_COMMENT_UPDATE";
    ClientRequestType["FILE_FLOW_REQUEST_COMMENT_UPLOAD"] = "FILE_FLOW_REQUEST_COMMENT_UPLOAD";
    ClientRequestType["FILE_FLOW_REQUEST_COMMENT_DELETE"] = "FILE_FLOW_REQUEST_COMMENT_DELETE";
    ClientRequestType["SESSION_FLOW_REQUEST_COMMENT_CREATE"] = "SESSION_FLOW_REQUEST_COMMENT_CREATE";
    ClientRequestType["SESSION_FLOW_REQUEST_COMMENT_UPDATE"] = "SESSION_FLOW_REQUEST_COMMENT_UPDATE";
    ClientRequestType["SESSION_FLOW_REQUEST_COMMENT_UPLOAD"] = "SESSION_FLOW_REQUEST_COMMENT_UPLOAD";
    ClientRequestType["SESSION_FLOW_REQUEST_COMMENT_DELETE"] = "SESSION_FLOW_REQUEST_COMMENT_DELETE";
    ClientRequestType["SIGN_FLOW_REQUEST_COMMENT_CREATE"] = "SIGN_FLOW_REQUEST_COMMENT_CREATE";
    ClientRequestType["SIGN_FLOW_REQUEST_COMMENT_UPDATE"] = "SIGN_FLOW_REQUEST_COMMENT_UPDATE";
    ClientRequestType["SIGN_FLOW_REQUEST_COMMENT_UPLOAD"] = "SIGN_FLOW_REQUEST_COMMENT_UPLOAD";
    ClientRequestType["SIGN_FLOW_REQUEST_COMMENT_DELETE"] = "SIGN_FLOW_REQUEST_COMMENT_DELETE";
    ClientRequestType["BOARD_REQUEST_CREATE_WAITING_USER"] = "BOARD_REQUEST_CREATE_WAITING_USER";
    ClientRequestType["BOARD_REQUEST_UPDATE_WAITING_USER"] = "BOARD_REQUEST_UPDATE_WAITING_USER";
    ClientRequestType["BOARD_REQUEST_DELETE_WAITING_USER"] = "BOARD_REQUEST_DELETE_WAITING_USER";
    ClientRequestType["BOARD_REQUEST_UPDATE_RESOURCE"] = "BOARD_REQUEST_UPDATE_RESOURCE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_CREATE"] = "BOARD_REQUEST_TRANSACTION_CREATE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_DELETE"] = "BOARD_REQUEST_TRANSACTION_DELETE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPDATE"] = "BOARD_REQUEST_TRANSACTION_UPDATE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_COPY"] = "BOARD_REQUEST_TRANSACTION_COPY";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_READ"] = "BOARD_REQUEST_TRANSACTION_READ";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_STEP_SUBMIT"] = "BOARD_REQUEST_TRANSACTION_STEP_SUBMIT";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT"] = "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPDATE_ATTACHMENT"] = "BOARD_REQUEST_TRANSACTION_UPDATE_ATTACHMENT";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_DELETE_ATTACHMENT"] = "BOARD_REQUEST_TRANSACTION_DELETE_ATTACHMENT";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_RESOURCE"] = "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_RESOURCE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_COMMENT_CREATE"] = "BOARD_REQUEST_TRANSACTION_COMMENT_CREATE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_COMMENT_UPDATE"] = "BOARD_REQUEST_TRANSACTION_COMMENT_UPDATE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_COMMENT_DELETE"] = "BOARD_REQUEST_TRANSACTION_COMMENT_DELETE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_COMMENT_UPLOAD"] = "BOARD_REQUEST_TRANSACTION_COMMENT_UPLOAD";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_FILE"] = "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_FILE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_URL"] = "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_URL";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_STEP_SUBMIT_BATCH"] = "BOARD_REQUEST_TRANSACTION_STEP_SUBMIT_BATCH";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_VIEW"] = "BOARD_REQUEST_TRANSACTION_VIEW";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_RESET_STATUS"] = "BOARD_REQUEST_TRANSACTION_RESET_STATUS";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_REOPEN"] = "BOARD_REQUEST_TRANSACTION_REOPEN";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_STEP_REOPEN"] = "BOARD_REQUEST_TRANSACTION_STEP_REOPEN";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_DELETE_BATCH"] = "BOARD_REQUEST_TRANSACTION_DELETE_BATCH";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE"] = "BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE_URL"] = "BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE_URL";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_DOWNLOAD_RESOURCE"] = "BOARD_REQUEST_TRANSACTION_DOWNLOAD_RESOURCE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_REMINDER_CREATE"] = "BOARD_REQUEST_TRANSACTION_REMINDER_CREATE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_REMINDER_UPDATE"] = "BOARD_REQUEST_TRANSACTION_REMINDER_UPDATE";
    ClientRequestType["BOARD_REQUEST_TRANSACTION_REMINDER_DELETE"] = "BOARD_REQUEST_TRANSACTION_REMINDER_DELETE";
    ClientRequestType["BOARD_REQUEST_SET_FEED_UNREAD"] = "BOARD_REQUEST_SET_FEED_UNREAD";
    ClientRequestType["BOARD_REQUEST_SET_ISTEMP_OFF"] = "BOARD_REQUEST_SET_ISTEMP_OFF";
    ClientRequestType["BOARD_REQUEST_UPDATE_RSVP"] = "BOARD_REQUEST_UPDATE_RSVP";
    ClientRequestType["BOARD_REQUEST_CREATE_PIN"] = "BOARD_REQUEST_CREATE_PIN";
    ClientRequestType["BOARD_REQUEST_DELETE_PIN"] = "BOARD_REQUEST_DELETE_PIN";
    ClientRequestType["BOARD_REQUEST_READ_FLAT_PINS"] = "BOARD_REQUEST_READ_FLAT_PINS";
    ClientRequestType["BOARD_REQUEST_SET_BOARD_TYPE"] = "BOARD_REQUEST_SET_BOARD_TYPE";
    ClientRequestType["BOARD_REQUEST_SET_ACTIVE"] = "BOARD_REQUEST_SET_ACTIVE";
    ClientRequestType["BOARD_REQUEST_WORKFLOW_CREATE"] = "BOARD_REQUEST_WORKFLOW_CREATE";
    ClientRequestType["BOARD_REQUEST_WORKFLOW_UPDATE"] = "BOARD_REQUEST_WORKFLOW_UPDATE";
    ClientRequestType["BOARD_REQUEST_WORKFLOW_DELETE"] = "BOARD_REQUEST_WORKFLOW_DELETE";
    ClientRequestType["BOARD_REQUEST_READ_PREDECESSORS"] = "BOARD_REQUEST_READ_PREDECESSORS";
    ClientRequestType["WORKFLOW_REQUEST_CREATE_TEMPLATE"] = "WORKFLOW_REQUEST_CREATE_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_UPDATE_TEMPLATE"] = "WORKFLOW_REQUEST_UPDATE_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_DELETE_TEMPLATE"] = "WORKFLOW_REQUEST_DELETE_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_LIST_TEMPLATE"] = "WORKFLOW_REQUEST_LIST_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_USE_TEMPLATE"] = "WORKFLOW_REQUEST_USE_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_COPY_TEMPLATE"] = "WORKFLOW_REQUEST_COPY_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_LIST_PREBUILT_TEMPLATE"] = "WORKFLOW_REQUEST_LIST_PREBUILT_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW"] = "WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW";
    ClientRequestType["WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW_STEP"] = "WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW_STEP";
    ClientRequestType["WORKFLOW_REQUEST_SKIP_BOARD_WORKFLOW_STEP"] = "WORKFLOW_REQUEST_SKIP_BOARD_WORKFLOW_STEP";
    ClientRequestType["WORKFLOW_REQUEST_CREATE_WORKFLOW"] = "WORKFLOW_REQUEST_CREATE_WORKFLOW";
    ClientRequestType["WORKFLOW_REQUEST_UPDATE_WORKFLOW"] = "WORKFLOW_REQUEST_UPDATE_WORKFLOW";
    ClientRequestType["WORKFLOW_REQUEST_DELETE_WORKFLOW"] = "WORKFLOW_REQUEST_DELETE_WORKFLOW";
    ClientRequestType["WORKFLOW_REQUEST_UPDATE_STATUS"] = "WORKFLOW_REQUEST_UPDATE_STATUS";
    ClientRequestType["WORKFLOW_REQUEST_RESTART"] = "WORKFLOW_REQUEST_RESTART";
    ClientRequestType["WORKFLOW_REQUEST_COMPLETE"] = "WORKFLOW_REQUEST_COMPLETE";
    ClientRequestType["WORKFLOW_REQUEST_CANCEL"] = "WORKFLOW_REQUEST_CANCEL";
    ClientRequestType["WORKFLOW_REQUEST_REOPEN_STEP"] = "WORKFLOW_REQUEST_REOPEN_STEP";
    ClientRequestType["WORKFLOW_REQUEST_SKIP_STEP"] = "WORKFLOW_REQUEST_SKIP_STEP";
    ClientRequestType["WORKFLOW_REQUEST_INCREASE_USED_COUNT"] = "WORKFLOW_REQUEST_INCREASE_USED_COUNT";
    ClientRequestType["WORKFLOW_REQUEST_COPY_AS_TEMPLATE"] = "WORKFLOW_REQUEST_COPY_AS_TEMPLATE";
    ClientRequestType["WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_FOLDER_COUNT"] = "WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_FOLDER_COUNT";
    ClientRequestType["WORKFLOW_REQUEST_COPY_WORKFLOW"] = "WORKFLOW_REQUEST_COPY_WORKFLOW";
    ClientRequestType["WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_TEMPLATE_COUNT"] = "WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_TEMPLATE_COUNT";
    ClientRequestType["WORKFLOW_REQUEST_MOVE_TEMPLATE"] = "WORKFLOW_REQUEST_MOVE_TEMPLATE";
    ClientRequestType["BOARD_REQUEST_CREATE_PROPERTY"] = "BOARD_REQUEST_CREATE_PROPERTY";
    ClientRequestType["BOARD_REQUEST_UPDATE_PROPERTY"] = "BOARD_REQUEST_UPDATE_PROPERTY";
    ClientRequestType["BOARD_REQUEST_DELETE_PROPERTY"] = "BOARD_REQUEST_DELETE_PROPERTY";
    ClientRequestType["BOARD_REQUEST_UPDATE_FEED_REACTION"] = "BOARD_REQUEST_UPDATE_FEED_REACTION";
    ClientRequestType["BOARD_REQUEST_CREATE_BROADCAST"] = "BOARD_REQUEST_CREATE_BROADCAST";
    ClientRequestType["BOARD_REQUEST_UPDATE_BROADCAST"] = "BOARD_REQUEST_UPDATE_BROADCAST";
    ClientRequestType["BOARD_REQUEST_DELETE_BROADCAST"] = "BOARD_REQUEST_DELETE_BROADCAST";
    ClientRequestType["BOARD_REQUEST_UPDATE_MEETING_TRANSCRIPTION"] = "BOARD_REQUEST_UPDATE_MEETING_TRANSCRIPTION";
    ClientRequestType["BOARD_REQUEST_DOWNLOAD_MEETING_TRANSCRIPTION"] = "BOARD_REQUEST_DOWNLOAD_MEETING_TRANSCRIPTION";
    ClientRequestType["BOARD_REQUEST_COPY_MEETING_TRANSCRIPTION"] = "BOARD_REQUEST_COPY_MEETING_TRANSCRIPTION";
    ClientRequestType["BOARD_REQUEST_TRANSCRIPT_RESOURCE"] = "BOARD_REQUEST_TRANSCRIPT_RESOURCE";
    ClientRequestType["BOARD_REQUEST_UPDATE_MEETING_SUMMARY"] = "BOARD_REQUEST_UPDATE_MEETING_SUMMARY";
    ClientRequestType["BOARD_REQUEST_UNITTEST"] = "BOARD_REQUEST_UNITTEST";
    ClientRequestType["BOARD_REQUEST_MAX"] = "BOARD_REQUEST_MAX";
    ClientRequestType["SERVER_AUDIO_CAPACITY"] = "SERVER_AUDIO_CAPACITY";
    ClientRequestType["SERVER_PBX_REPORT"] = "SERVER_PBX_REPORT";
    ClientRequestType["SERVER_DESKTOP_SHARE_CAPACITY"] = "SERVER_DESKTOP_SHARE_CAPACITY";
    ClientRequestType["SERVER_PROBE"] = "SERVER_PROBE";
    ClientRequestType["SERVER_OBJECT_READ"] = "SERVER_OBJECT_READ";
    ClientRequestType["SERVER_OBJECT_SUBSCRIBE"] = "SERVER_OBJECT_SUBSCRIBE";
    ClientRequestType["SERVER_OBJECT_UNSUBSCRIBE"] = "SERVER_OBJECT_UNSUBSCRIBE";
    ClientRequestType["SERVER_OBJECT_ACTIVITY"] = "SERVER_OBJECT_ACTIVITY";
    ClientRequestType["SERVER_OBJECT_AUDIT"] = "SERVER_OBJECT_AUDIT";
    ClientRequestType["SERVER_OBJECT_WRITE"] = "SERVER_OBJECT_WRITE";
    ClientRequestType["SERVER_OBJECT_STATS"] = "SERVER_OBJECT_STATS";
    ClientRequestType["SERVER_OBJECT_LIST_SERVERS"] = "SERVER_OBJECT_LIST_SERVERS";
    ClientRequestType["SERVER_FILE_DOWNLOAD"] = "SERVER_FILE_DOWNLOAD";
    ClientRequestType["SERVER_FILE_UPLOAD"] = "SERVER_FILE_UPLOAD";
    ClientRequestType["SERVER_FILE_PREVIEW"] = "SERVER_FILE_PREVIEW";
    ClientRequestType["SERVER_USER_DISABLE"] = "SERVER_USER_DISABLE";
    ClientRequestType["SERVER_USER_ENABLE"] = "SERVER_USER_ENABLE";
    ClientRequestType["SERVER_USER_LEVEL_UPGRADE"] = "SERVER_USER_LEVEL_UPGRADE";
    ClientRequestType["SERVER_USER_LEVEL_DOWNGRADE"] = "SERVER_USER_LEVEL_DOWNGRADE";
    ClientRequestType["SERVER_USER_READ"] = "SERVER_USER_READ";
    ClientRequestType["SERVER_USER_UP_SIZE"] = "SERVER_USER_UP_SIZE";
    ClientRequestType["SERVER_USER_DOWN_SIZE"] = "SERVER_USER_DOWN_SIZE";
    ClientRequestType["SERVER_REDO_JOB"] = "SERVER_REDO_JOB";
    ClientRequestType["SERVER_FORWARD_REQUEST"] = "SERVER_FORWARD_REQUEST";
    ClientRequestType["SERVER_UPLOAD_CRASH_REPORT"] = "SERVER_UPLOAD_CRASH_REPORT";
    ClientRequestType["SERVER_LIST_CRASH_REPORTS"] = "SERVER_LIST_CRASH_REPORTS";
    ClientRequestType["SERVER_DOWNLOAD_CRASH_REPORT"] = "SERVER_DOWNLOAD_CRASH_REPORT";
    ClientRequestType["SERVER_DELETE_CRASH_REPORT"] = "SERVER_DELETE_CRASH_REPORT";
    ClientRequestType["SERVER_READ_STATISTICS"] = "SERVER_READ_STATISTICS";
    ClientRequestType["SERVER_UPDATE_STATISTICS"] = "SERVER_UPDATE_STATISTICS";
    ClientRequestType["SERVER_TOKEN_DECODE"] = "SERVER_TOKEN_DECODE";
    ClientRequestType["SERVER_SERVICE_PROVIDERS_READ"] = "SERVER_SERVICE_PROVIDERS_READ";
    ClientRequestType["SERVER_SERVICE_PROVIDERS_UPDATE"] = "SERVER_SERVICE_PROVIDERS_UPDATE";
    ClientRequestType["SERVER_IDP_CONFIG_READ"] = "SERVER_IDP_CONFIG_READ";
    ClientRequestType["SERVER_WEBAPP_READ"] = "SERVER_WEBAPP_READ";
    ClientRequestType["SERVER_SYSTEM_CONFIG_READ"] = "SERVER_SYSTEM_CONFIG_READ";
    ClientRequestType["SERVER_SYSTEM_CONFIG_UPDATE"] = "SERVER_SYSTEM_CONFIG_UPDATE";
    ClientRequestType["SERVER_REQUEST_VALIDATION_CODE"] = "SERVER_REQUEST_VALIDATION_CODE";
    ClientRequestType["SERVER_GROUP_LIST_ADD"] = "SERVER_GROUP_LIST_ADD";
    ClientRequestType["SERVER_GROUP_LIST_REMOVE"] = "SERVER_GROUP_LIST_REMOVE";
    ClientRequestType["SERVER_CHART_READ"] = "SERVER_CHART_READ";
    ClientRequestType["SERVER_GROUP_USAGE_REPORT"] = "SERVER_GROUP_USAGE_REPORT";
    ClientRequestType["SERVER_PROBE_REPORT"] = "SERVER_PROBE_REPORT";
    ClientRequestType["SERVER_STATISTICS_REPORT"] = "SERVER_STATISTICS_REPORT";
    ClientRequestType["SERVER_REQUEST_MAX"] = "SERVER_REQUEST_MAX";
    ClientRequestType["AGENT_REQUEST_LIST_FOLDER"] = "AGENT_REQUEST_LIST_FOLDER";
    ClientRequestType["AGENT_REQUEST_DOWNLOAD_FILE"] = "AGENT_REQUEST_DOWNLOAD_FILE";
    ClientRequestType["AGENT_REQUEST_PREVIEW_FILE"] = "AGENT_REQUEST_PREVIEW_FILE";
    ClientRequestType["AGENT_REQUEST_UPLOAD_RESOURCE"] = "AGENT_REQUEST_UPLOAD_RESOURCE";
    ClientRequestType["AGENT_REQUEST_UPLOAD_FILE"] = "AGENT_REQUEST_UPLOAD_FILE";
    ClientRequestType["AGENT_REQUEST_UPLOAD_FILE_RESOURCE"] = "AGENT_REQUEST_UPLOAD_FILE_RESOURCE";
    ClientRequestType["AGENT_REQUEST_CREATE_FOLDER"] = "AGENT_REQUEST_CREATE_FOLDER";
    ClientRequestType["AGENT_REQUEST_MOVE_ENTRY"] = "AGENT_REQUEST_MOVE_ENTRY";
    ClientRequestType["AGENT_REQUEST_DELETE_ENTRY"] = "AGENT_REQUEST_DELETE_ENTRY";
    ClientRequestType["AGENT_REQUEST_QUERY_UPLOAD_PROGRESS"] = "AGENT_REQUEST_QUERY_UPLOAD_PROGRESS";
    ClientRequestType["AGENT_PUBLISH_RESPONSE"] = "AGENT_PUBLISH_RESPONSE";
    ClientRequestType["AGENT_SERVE_FILE"] = "AGENT_SERVE_FILE";
    ClientRequestType["AGENT_REQUEST_ONLINE"] = "AGENT_REQUEST_ONLINE";
    ClientRequestType["AGENT_REQUEST_OFFLINE"] = "AGENT_REQUEST_OFFLINE";
    ClientRequestType["AGENT_REQUEST_MAX"] = "AGENT_REQUEST_MAX";
    ClientRequestType["WEBAPP_REQUEST_CREATE"] = "WEBAPP_REQUEST_CREATE";
    ClientRequestType["WEBAPP_REQUEST_READ"] = "WEBAPP_REQUEST_READ";
    ClientRequestType["WEBAPP_REQUEST_UPDATE"] = "WEBAPP_REQUEST_UPDATE";
    ClientRequestType["WEBAPP_REQUEST_DELETE"] = "WEBAPP_REQUEST_DELETE";
    ClientRequestType["WEBAPP_REQUEST_LIST"] = "WEBAPP_REQUEST_LIST";
    ClientRequestType["WEBAPP_REQUEST_LIST_BOT"] = "WEBAPP_REQUEST_LIST_BOT";
    ClientRequestType["WEBAPP_REQUEST_LIST_EMBEDDED"] = "WEBAPP_REQUEST_LIST_EMBEDDED";
    ClientRequestType["WEBAPP_REQUEST_LIST_SUBSCRIPTION"] = "WEBAPP_REQUEST_LIST_SUBSCRIPTION";
    ClientRequestType["WEBAPP_REQUEST_LIST_INBOX_BOT"] = "WEBAPP_REQUEST_LIST_INBOX_BOT";
    ClientRequestType["WEBAPP_REQUEST_DOWNLOAD_RESOURCE"] = "WEBAPP_REQUEST_DOWNLOAD_RESOURCE";
    ClientRequestType["WEBAPP_REQUEST_UPLOAD_RESOURCE"] = "WEBAPP_REQUEST_UPLOAD_RESOURCE";
    ClientRequestType["WEBAPP_REQUEST_UPLOAD_PICTURE"] = "WEBAPP_REQUEST_UPLOAD_PICTURE";
    ClientRequestType["WEBAPP_REQUEST_CREATE_TOKEN"] = "WEBAPP_REQUEST_CREATE_TOKEN";
    ClientRequestType["WEBAPP_REQUEST_MAX"] = "WEBAPP_REQUEST_MAX";
    ClientRequestType["GROUP_REQUEST_CREATE"] = "GROUP_REQUEST_CREATE";
    ClientRequestType["GROUP_REQUEST_READ_CAPABILITY"] = "GROUP_REQUEST_READ_CAPABILITY";
    ClientRequestType["GROUP_REQUEST_REGISTER"] = "GROUP_REQUEST_REGISTER";
    ClientRequestType["GROUP_REQUEST_LIST_AVAILABLE_BASE_DOMAINS"] = "GROUP_REQUEST_LIST_AVAILABLE_BASE_DOMAINS";
    ClientRequestType["GROUP_REQUEST_CHECK_DOMAIN_AVAILABILITY"] = "GROUP_REQUEST_CHECK_DOMAIN_AVAILABILITY";
    ClientRequestType["GROUP_REQUEST_UNREGISTER"] = "GROUP_REQUEST_UNREGISTER";
    ClientRequestType["GROUP_REQUEST_READ"] = "GROUP_REQUEST_READ";
    ClientRequestType["GROUP_REQUEST_READ_MEMBERS"] = "GROUP_REQUEST_READ_MEMBERS";
    ClientRequestType["GROUP_REQUEST_READ_SORT_MEMBERS"] = "GROUP_REQUEST_READ_SORT_MEMBERS";
    ClientRequestType["GROUP_REQUEST_READ_MANAGEMENT_MEMBERS"] = "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS";
    ClientRequestType["GROUP_REQUEST_EXPORT_MEMBERS"] = "GROUP_REQUEST_EXPORT_MEMBERS";
    ClientRequestType["GROUP_REQUEST_EXPORT_USER_ACTIVITIES"] = "GROUP_REQUEST_EXPORT_USER_ACTIVITIES";
    ClientRequestType["GROUP_REQUEST_EXPORT_CLIENT_ENGAGEMENT"] = "GROUP_REQUEST_EXPORT_CLIENT_ENGAGEMENT";
    ClientRequestType["GROUP_REQUEST_EXPORT_INTERNAL_USER_ENGAGEMENT"] = "GROUP_REQUEST_EXPORT_INTERNAL_USER_ENGAGEMENT";
    ClientRequestType["GROUP_REQUEST_EXPORT_CLIENT_COVERAGE"] = "GROUP_REQUEST_EXPORT_CLIENT_COVERAGE";
    ClientRequestType["GROUP_REQUEST_EXPORT_SOCIAL_ENGAGEMENT"] = "GROUP_REQUEST_EXPORT_SOCIAL_ENGAGEMENT";
    ClientRequestType["GROUP_REQUEST_READ_MANAGEMENT_USER_ACTIVITIES"] = "GROUP_REQUEST_READ_MANAGEMENT_USER_ACTIVITIES";
    ClientRequestType["GROUP_REQUEST_READ_MANAGEMENT_USER_BOARDS"] = "GROUP_REQUEST_READ_MANAGEMENT_USER_BOARDS";
    ClientRequestType["GROUP_REQUEST_EXPORT_SERVICE_REQUEST_SUMMARY"] = "GROUP_REQUEST_EXPORT_SERVICE_REQUEST_SUMMARY";
    ClientRequestType["GROUP_REQUEST_EXPORT_SERVICE_REQUEST_AGENT_SUMMARY"] = "GROUP_REQUEST_EXPORT_SERVICE_REQUEST_AGENT_SUMMARY";
    ClientRequestType["GROUP_REQUEST_EXPORT_ACD_SUMMARY"] = "GROUP_REQUEST_EXPORT_ACD_SUMMARY";
    ClientRequestType["GROUP_REQUEST_EXPORT_ACD_AGENT_SUMMARY"] = "GROUP_REQUEST_EXPORT_ACD_AGENT_SUMMARY";
    ClientRequestType["GROUP_REQUEST_EXPORT_CLIENT_USAGE"] = "GROUP_REQUEST_EXPORT_CLIENT_USAGE";
    ClientRequestType["GROUP_REQUEST_EXPORT_INTERNAL_USAGE"] = "GROUP_REQUEST_EXPORT_INTERNAL_USAGE";
    ClientRequestType["GROUP_REQUEST_EXPORT_DAILY_ORG_ACTIVITY"] = "GROUP_REQUEST_EXPORT_DAILY_ORG_ACTIVITY";
    ClientRequestType["GROUP_REQUEST_EXPORT_DAILY_USER_ACTIVITY"] = "GROUP_REQUEST_EXPORT_DAILY_USER_ACTIVITY";
    ClientRequestType["GROUP_REQUEST_UPDATE"] = "GROUP_REQUEST_UPDATE";
    ClientRequestType["GROUP_REQUEST_UPDATE_ALIAS"] = "GROUP_REQUEST_UPDATE_ALIAS";
    ClientRequestType["GROUP_REQUEST_UPDATE_MEMBER_ALIAS"] = "GROUP_REQUEST_UPDATE_MEMBER_ALIAS";
    ClientRequestType["GROUP_REQUEST_READ_ALIAS"] = "GROUP_REQUEST_READ_ALIAS";
    ClientRequestType["GROUP_REQUEST_CANCEL"] = "GROUP_REQUEST_CANCEL";
    ClientRequestType["GROUP_REQUEST_READ_USAGE"] = "GROUP_REQUEST_READ_USAGE";
    ClientRequestType["GROUP_REQUEST_SUBSCRIBE"] = "GROUP_REQUEST_SUBSCRIBE";
    ClientRequestType["GROUP_REQUEST_UNSUBSCRIBE"] = "GROUP_REQUEST_UNSUBSCRIBE";
    ClientRequestType["GROUP_REQUEST_READ_APP_ASSOCIATION"] = "GROUP_REQUEST_READ_APP_ASSOCIATION";
    ClientRequestType["GROUP_REQUEST_READ_APP_ASSETLINKS"] = "GROUP_REQUEST_READ_APP_ASSETLINKS";
    ClientRequestType["GROUP_REQUEST_READ_TELEPHONY_DOMAIN"] = "GROUP_REQUEST_READ_TELEPHONY_DOMAIN";
    ClientRequestType["GROUP_REQUEST_INVITE"] = "GROUP_REQUEST_INVITE";
    ClientRequestType["GROUP_REQUEST_INVITE_CSV_IMPORT"] = "GROUP_REQUEST_INVITE_CSV_IMPORT";
    ClientRequestType["GROUP_REQUEST_INVITE_CSV_IMPORT_BY_INTERNAL"] = "GROUP_REQUEST_INVITE_CSV_IMPORT_BY_INTERNAL";
    ClientRequestType["GROUP_REQUEST_JOIN"] = "GROUP_REQUEST_JOIN";
    ClientRequestType["GROUP_REQUEST_JOIN_VIA_INVITATION"] = "GROUP_REQUEST_JOIN_VIA_INVITATION";
    ClientRequestType["GROUP_REQUEST_LEAVE"] = "GROUP_REQUEST_LEAVE";
    ClientRequestType["GROUP_REQUEST_RESEND_INVITATION_EMAIL"] = "GROUP_REQUEST_RESEND_INVITATION_EMAIL";
    ClientRequestType["GROUP_REQUEST_INVITATION_CONFIRM_EMAIL"] = "GROUP_REQUEST_INVITATION_CONFIRM_EMAIL";
    ClientRequestType["GROUP_REQUEST_RESEND_INVITATION_SMS"] = "GROUP_REQUEST_RESEND_INVITATION_SMS";
    ClientRequestType["GROUP_REQUEST_INVITATION_CONFIRM_SMS"] = "GROUP_REQUEST_INVITATION_CONFIRM_SMS";
    ClientRequestType["GROUP_REQUEST_EXPEL"] = "GROUP_REQUEST_EXPEL";
    ClientRequestType["GROUP_REQUEST_REMOVE_MEMBER"] = "GROUP_REQUEST_REMOVE_MEMBER";
    ClientRequestType["GROUP_REQUEST_SET_ACCESS_TYPE"] = "GROUP_REQUEST_SET_ACCESS_TYPE";
    ClientRequestType["GROUP_REQUEST_VIEW_INVITATION"] = "GROUP_REQUEST_VIEW_INVITATION";
    ClientRequestType["GROUP_REQUEST_DOWNLOAD_RESOURCE"] = "GROUP_REQUEST_DOWNLOAD_RESOURCE";
    ClientRequestType["GROUP_REQUEST_UPLOAD_RESOURCE"] = "GROUP_REQUEST_UPLOAD_RESOURCE";
    ClientRequestType["GROUP_REQUEST_USER_READ"] = "GROUP_REQUEST_USER_READ";
    ClientRequestType["GROUP_REQUEST_USER_UPDATE"] = "GROUP_REQUEST_USER_UPDATE";
    ClientRequestType["GROUP_REQUEST_USER_UPDATE_EMAIL"] = "GROUP_REQUEST_USER_UPDATE_EMAIL";
    ClientRequestType["GROUP_REQUEST_USER_UPDATE_EMAIL_PHONE_NUMBER"] = "GROUP_REQUEST_USER_UPDATE_EMAIL_PHONE_NUMBER";
    ClientRequestType["GROUP_REQUEST_USER_DISABLE"] = "GROUP_REQUEST_USER_DISABLE";
    ClientRequestType["GROUP_REQUEST_USER_ENABLE"] = "GROUP_REQUEST_USER_ENABLE";
    ClientRequestType["GROUP_REQUEST_USER_TRANSFER"] = "GROUP_REQUEST_USER_TRANSFER";
    ClientRequestType["GROUP_REQUEST_USER_ARCHIVE"] = "GROUP_REQUEST_USER_ARCHIVE";
    ClientRequestType["GROUP_REQUEST_USERS_READ"] = "GROUP_REQUEST_USERS_READ";
    ClientRequestType["GROUP_REQUEST_READ_USER_BUSY_TIME"] = "GROUP_REQUEST_READ_USER_BUSY_TIME";
    ClientRequestType["GROUP_REQUEST_IMPORT_REDEEM_URL"] = "GROUP_REQUEST_IMPORT_REDEEM_URL";
    ClientRequestType["GROUP_REQUEST_RESET_REDEEM_URL"] = "GROUP_REQUEST_RESET_REDEEM_URL";
    ClientRequestType["GROUP_REQUEST_GET_REDEEM_URL"] = "GROUP_REQUEST_GET_REDEEM_URL";
    ClientRequestType["GROUP_REQUEST_CREATE_BOARD_PROPERTY"] = "GROUP_REQUEST_CREATE_BOARD_PROPERTY";
    ClientRequestType["GROUP_REQUEST_UPDATE_BOARD_PROPERTY"] = "GROUP_REQUEST_UPDATE_BOARD_PROPERTY";
    ClientRequestType["GROUP_REQUEST_DELETE_BOARD_PROPERTY"] = "GROUP_REQUEST_DELETE_BOARD_PROPERTY";
    ClientRequestType["GROUP_REQUEST_BOARD_LEAVE"] = "GROUP_REQUEST_BOARD_LEAVE";
    ClientRequestType["GROUP_REQUEST_BOARD_CREATE"] = "GROUP_REQUEST_BOARD_CREATE";
    ClientRequestType["GROUP_REQUEST_BOARD_DELETE"] = "GROUP_REQUEST_BOARD_DELETE";
    ClientRequestType["GROUP_REQUEST_BOARD_ADD_MEMBER"] = "GROUP_REQUEST_BOARD_ADD_MEMBER";
    ClientRequestType["GROUP_REQUEST_BOARD_REMOVE_MEMBER"] = "GROUP_REQUEST_BOARD_REMOVE_MEMBER";
    ClientRequestType["GROUP_REQUEST_BOARD_UPDATE_MEMBER"] = "GROUP_REQUEST_BOARD_UPDATE_MEMBER";
    ClientRequestType["GROUP_REQUEST_BOARD_UPDATE"] = "GROUP_REQUEST_BOARD_UPDATE";
    ClientRequestType["GROUP_REQUEST_SESSION_SCHEDULE"] = "GROUP_REQUEST_SESSION_SCHEDULE";
    ClientRequestType["GROUP_REQUEST_CREATE_RELATION"] = "GROUP_REQUEST_CREATE_RELATION";
    ClientRequestType["GROUP_REQUEST_INVITE_AND_CREATE_RELATION"] = "GROUP_REQUEST_INVITE_AND_CREATE_RELATION";
    ClientRequestType["GROUP_REQUEST_CONFIRM_RELATION"] = "GROUP_REQUEST_CONFIRM_RELATION";
    ClientRequestType["GROUP_REQUEST_TRANSFER_RELATION"] = "GROUP_REQUEST_TRANSFER_RELATION";
    ClientRequestType["GROUP_REQUEST_DELETE_RELATION"] = "GROUP_REQUEST_DELETE_RELATION";
    ClientRequestType["GROUP_REQUEST_UPDATE_RELATION"] = "GROUP_REQUEST_UPDATE_RELATION";
    ClientRequestType["GROUP_REQUEST_INVITE_BOARD_REQUESTING_USER"] = "GROUP_REQUEST_INVITE_BOARD_REQUESTING_USER";
    ClientRequestType["GROUP_REQUEST_CREATE_BOT_RELATION"] = "GROUP_REQUEST_CREATE_BOT_RELATION";
    ClientRequestType["GROUP_REQUEST_DELETE_BOT_RELATION"] = "GROUP_REQUEST_DELETE_BOT_RELATION";
    ClientRequestType["GROUP_REQUEST_CREATE_INTEGRATION"] = "GROUP_REQUEST_CREATE_INTEGRATION";
    ClientRequestType["GROUP_REQUEST_UPDATE_INTEGRATION"] = "GROUP_REQUEST_UPDATE_INTEGRATION";
    ClientRequestType["GROUP_REQUEST_DELETE_INTEGRATION"] = "GROUP_REQUEST_DELETE_INTEGRATION";
    ClientRequestType["GROUP_REQUEST_VERIFY_INTEGRATION"] = "GROUP_REQUEST_VERIFY_INTEGRATION";
    ClientRequestType["GROUP_REQUEST_GET_INTEGRATION_USER_ACCESSTOKEN"] = "GROUP_REQUEST_GET_INTEGRATION_USER_ACCESSTOKEN";
    ClientRequestType["GROUP_REQUEST_READ_TASKS"] = "GROUP_REQUEST_READ_TASKS";
    ClientRequestType["GROUP_REQUEST_STRIPE_WEBHOOK"] = "GROUP_REQUEST_STRIPE_WEBHOOK";
    ClientRequestType["GROUP_REQUEST_STRIPE_CUSTOMER"] = "GROUP_REQUEST_STRIPE_CUSTOMER";
    ClientRequestType["GROUP_REQUEST_STRIPE_SUBSCRIBE"] = "GROUP_REQUEST_STRIPE_SUBSCRIBE";
    ClientRequestType["GROUP_REQUEST_STRIPE_PRICE"] = "GROUP_REQUEST_STRIPE_PRICE";
    ClientRequestType["GROUP_REQUEST_STRIPE_INVOICES"] = "GROUP_REQUEST_STRIPE_INVOICES";
    ClientRequestType["GROUP_REQUEST_STRIPE_UPCOMING_INVOICE"] = "GROUP_REQUEST_STRIPE_UPCOMING_INVOICE";
    ClientRequestType["GROUP_REQUEST_STRIPE_COUPON"] = "GROUP_REQUEST_STRIPE_COUPON";
    ClientRequestType["GROUP_REQUEST_STRIPE_PUBLISHABLE_KEY"] = "GROUP_REQUEST_STRIPE_PUBLISHABLE_KEY";
    ClientRequestType["GROUP_REQUEST_CREATE_TEAM"] = "GROUP_REQUEST_CREATE_TEAM";
    ClientRequestType["GROUP_REQUEST_UPDATE_TEAM"] = "GROUP_REQUEST_UPDATE_TEAM";
    ClientRequestType["GROUP_REQUEST_DELETE_TEAM"] = "GROUP_REQUEST_DELETE_TEAM";
    ClientRequestType["GROUP_REQUEST_CREATE_PUBLIC_TEAM"] = "GROUP_REQUEST_CREATE_PUBLIC_TEAM";
    ClientRequestType["GROUP_REQUEST_UPDATE_PUBLIC_TEAM"] = "GROUP_REQUEST_UPDATE_PUBLIC_TEAM";
    ClientRequestType["GROUP_REQUEST_DELETE_PUBLIC_TEAM"] = "GROUP_REQUEST_DELETE_PUBLIC_TEAM";
    ClientRequestType["GROUP_REQUEST_ADD_TEAM_MEMBER"] = "GROUP_REQUEST_ADD_TEAM_MEMBER";
    ClientRequestType["GROUP_REQUEST_REMOVE_TEAM_MEMBER"] = "GROUP_REQUEST_REMOVE_TEAM_MEMBER";
    ClientRequestType["GROUP_REQUEST_LEAVE_TEAM"] = "GROUP_REQUEST_LEAVE_TEAM";
    ClientRequestType["GROUP_REQUEST_REASSIGN_TEAM_OWNER"] = "GROUP_REQUEST_REASSIGN_TEAM_OWNER";
    ClientRequestType["GROUP_REQUEST_SET_TEAM_MEMBER_ACCESS_TYPE"] = "GROUP_REQUEST_SET_TEAM_MEMBER_ACCESS_TYPE";
    ClientRequestType["GROUP_REQUEST_ADD_TEAM_MANAGER"] = "GROUP_REQUEST_ADD_TEAM_MANAGER";
    ClientRequestType["GROUP_REQUEST_REMOVE_TEAM_MANAGER"] = "GROUP_REQUEST_REMOVE_TEAM_MANAGER";
    ClientRequestType["GROUP_REQUEST_ASSIGN_TELEPHONY_DOMAIN"] = "GROUP_REQUEST_ASSIGN_TELEPHONY_DOMAIN";
    ClientRequestType["GROUP_REQUEST_ADD_INTEGRATION_SUBSCRIBER"] = "GROUP_REQUEST_ADD_INTEGRATION_SUBSCRIBER";
    ClientRequestType["GROUP_REQUEST_REMOVE_INTEGRATION_SUBSCRIBER"] = "GROUP_REQUEST_REMOVE_INTEGRATION_SUBSCRIBER";
    ClientRequestType["GROUP_REQUEST_READ_INTEGRATION_SUBSCRIBERS"] = "GROUP_REQUEST_READ_INTEGRATION_SUBSCRIBERS";
    ClientRequestType["GROUP_REQUEST_SET_BOARD_MEMBER_ACCESS_TYPE"] = "GROUP_REQUEST_SET_BOARD_MEMBER_ACCESS_TYPE";
    ClientRequestType["GROUP_REQUEST_USER_READ_ACTIVITIES"] = "GROUP_REQUEST_USER_READ_ACTIVITIES";
    ClientRequestType["GROUP_REQUEST_READ_GROUP_ACTIVITIES"] = "GROUP_REQUEST_READ_GROUP_ACTIVITIES";
    ClientRequestType["GROUP_REQUEST_USER_POST_ACTIVITIES"] = "GROUP_REQUEST_USER_POST_ACTIVITIES";
    ClientRequestType["GROUP_REQUEST_SUBSCRIBE_SERVICE_REQUESTS"] = "GROUP_REQUEST_SUBSCRIBE_SERVICE_REQUESTS";
    ClientRequestType["GROUP_REQUEST_CREATE_ROLE"] = "GROUP_REQUEST_CREATE_ROLE";
    ClientRequestType["GROUP_REQUEST_UPDATE_ROLE"] = "GROUP_REQUEST_UPDATE_ROLE";
    ClientRequestType["GROUP_REQUEST_DELETE_ROLE"] = "GROUP_REQUEST_DELETE_ROLE";
    ClientRequestType["GROUP_REQUEST_USER_RESET_PASSWORD"] = "GROUP_REQUEST_USER_RESET_PASSWORD";
    ClientRequestType["GROUP_REQUEST_USER_UPDATE_PICTURES"] = "GROUP_REQUEST_USER_UPDATE_PICTURES";
    ClientRequestType["GROUP_REQUEST_USER_UPLOAD_PROFILE_PICTURES"] = "GROUP_REQUEST_USER_UPLOAD_PROFILE_PICTURES";
    ClientRequestType["GROUP_REQUEST_USER_UPLOAD_RESOURCE"] = "GROUP_REQUEST_USER_UPLOAD_RESOURCE";
    ClientRequestType["GROUP_REQUEST_CREATE_SOCIAL_CONNECTION"] = "GROUP_REQUEST_CREATE_SOCIAL_CONNECTION";
    ClientRequestType["GROUP_REQUEST_UPDATE_SOCIAL_CONNECTION"] = "GROUP_REQUEST_UPDATE_SOCIAL_CONNECTION";
    ClientRequestType["GROUP_REQUEST_DELETE_SOCIAL_CONNECTION"] = "GROUP_REQUEST_DELETE_SOCIAL_CONNECTION";
    ClientRequestType["GROUP_REQUEST_READ_SOCIAL_CONNECTION"] = "GROUP_REQUEST_READ_SOCIAL_CONNECTION";
    ClientRequestType["GROUP_REQUEST_JWT_TOKEN"] = "GROUP_REQUEST_JWT_TOKEN";
    ClientRequestType["GROUP_REQUEST_USER_RESET_PASSWORD_SMS"] = "GROUP_REQUEST_USER_RESET_PASSWORD_SMS";
    ClientRequestType["GROUP_REQUEST_CREATE_ROUTING_CHANNEL"] = "GROUP_REQUEST_CREATE_ROUTING_CHANNEL";
    ClientRequestType["GROUP_REQUEST_UPDATE_ROUTING_CHANNEL"] = "GROUP_REQUEST_UPDATE_ROUTING_CHANNEL";
    ClientRequestType["GROUP_REQUEST_DELETE_ROUTING_CHANNEL"] = "GROUP_REQUEST_DELETE_ROUTING_CHANNEL";
    ClientRequestType["GROUP_REQUEST_UPDATE_USER_INVITATION_TOKEN"] = "GROUP_REQUEST_UPDATE_USER_INVITATION_TOKEN";
    ClientRequestType["GROUP_REQUEST_READ_SUBSCRIPTION_BOARDS"] = "GROUP_REQUEST_READ_SUBSCRIPTION_BOARDS";
    ClientRequestType["GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARDS"] = "GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARDS";
    ClientRequestType["GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARD_COUNT"] = "GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARD_COUNT";
    ClientRequestType["GROUP_REQUEST_UPDATE_CRM_REPORT"] = "GROUP_REQUEST_UPDATE_CRM_REPORT";
    ClientRequestType["GROUP_REQUEST_READ_CRM_REPORT"] = "GROUP_REQUEST_READ_CRM_REPORT";
    ClientRequestType["GROUP_REQUEST_BOX_ACCESS_TOKEN"] = "GROUP_REQUEST_BOX_ACCESS_TOKEN";
    ClientRequestType["GROUP_REQUEST_SEARCH_USER_BOARD"] = "GROUP_REQUEST_SEARCH_USER_BOARD";
    ClientRequestType["GROUP_REQUEST_USER_BOARD_LOOKUP"] = "GROUP_REQUEST_USER_BOARD_LOOKUP";
    ClientRequestType["GROUP_REQUEST_USER_UPDATE_OUT_OF_OFFICE"] = "GROUP_REQUEST_USER_UPDATE_OUT_OF_OFFICE";
    ClientRequestType["GROUP_REQUEST_INVITE_AND_USE_WORKFLOW_TEMPLATE"] = "GROUP_REQUEST_INVITE_AND_USE_WORKFLOW_TEMPLATE";
    ClientRequestType["GROUP_REQUEST_USER_UPDATE_DISTRIBUTION_LIST"] = "GROUP_REQUEST_USER_UPDATE_DISTRIBUTION_LIST";
    ClientRequestType["GROUP_REQUEST_USER_DELETE_FUTURE_SESSIONS"] = "GROUP_REQUEST_USER_DELETE_FUTURE_SESSIONS";
    ClientRequestType["GROUP_REQUEST_USER_DELETE_SCHEDULE_BOARDS"] = "GROUP_REQUEST_USER_DELETE_SCHEDULE_BOARDS";
    ClientRequestType["GROUP_REQUEST_MAX"] = "GROUP_REQUEST_MAX";
    ClientRequestType["PRESENCE_REQUEST_READ"] = "PRESENCE_REQUEST_READ";
    ClientRequestType["PRESENCE_USER_REQUEST_READ"] = "PRESENCE_USER_REQUEST_READ";
    ClientRequestType["PRESENCE_REQUEST_MESSAGE"] = "PRESENCE_REQUEST_MESSAGE";
    ClientRequestType["PRESENCE_USER_REQUEST_UPDATE"] = "PRESENCE_USER_REQUEST_UPDATE";
    ClientRequestType["PRESENCE_REQUEST_MAX"] = "PRESENCE_REQUEST_MAX";
    ClientRequestType["PARTNER_REQUEST_CREATE"] = "PARTNER_REQUEST_CREATE";
    ClientRequestType["PARTNER_REQUEST_READ"] = "PARTNER_REQUEST_READ";
    ClientRequestType["PARTNER_REQUEST_UPDATE"] = "PARTNER_REQUEST_UPDATE";
    ClientRequestType["PARTNER_REQUEST_LIST"] = "PARTNER_REQUEST_LIST";
    ClientRequestType["PARTNER_REQUEST_ADD_MEMBER"] = "PARTNER_REQUEST_ADD_MEMBER";
    ClientRequestType["PARTNER_REQUEST_DELETE_MEMBER"] = "PARTNER_REQUEST_DELETE_MEMBER";
    ClientRequestType["PARTNER_REQUEST_VIEW_INVITATION"] = "PARTNER_REQUEST_VIEW_INVITATION";
    ClientRequestType["PARTNER_REQUEST_ADD_PLAN_CODE"] = "PARTNER_REQUEST_ADD_PLAN_CODE";
    ClientRequestType["PARTNER_REQUEST_DELETE_PLAN_CODE"] = "PARTNER_REQUEST_DELETE_PLAN_CODE";
    ClientRequestType["PARTNER_REQUEST_CREATE_INTEGRATION"] = "PARTNER_REQUEST_CREATE_INTEGRATION";
    ClientRequestType["PARTNER_REQUEST_UPDATE_INTEGRATION"] = "PARTNER_REQUEST_UPDATE_INTEGRATION";
    ClientRequestType["PARTNER_REQUEST_DELETE_INTEGRATION"] = "PARTNER_REQUEST_DELETE_INTEGRATION";
    ClientRequestType["PARTNER_REQUEST_READ_STATISTICS"] = "PARTNER_REQUEST_READ_STATISTICS";
    ClientRequestType["PARTNER_REQUEST_READ_PLAN_CODES"] = "PARTNER_REQUEST_READ_PLAN_CODES";
    ClientRequestType["PARTNER_REQUEST_READ_USAGE"] = "PARTNER_REQUEST_READ_USAGE";
    ClientRequestType["PARTNER_REQUEST_CREATE_GROUP"] = "PARTNER_REQUEST_CREATE_GROUP";
    ClientRequestType["PARTNER_REQUEST_LIST_GROUPS"] = "PARTNER_REQUEST_LIST_GROUPS";
    ClientRequestType["PARTNER_REQUEST_CREATE_USERS"] = "PARTNER_REQUEST_CREATE_USERS";
    ClientRequestType["PARTNER_REQUEST_CREATE_TELEPHONY_DOMAIN"] = "PARTNER_REQUEST_CREATE_TELEPHONY_DOMAIN";
    ClientRequestType["PARTNER_REQUEST_UPDATE_TELEPHONY_DOMAIN"] = "PARTNER_REQUEST_UPDATE_TELEPHONY_DOMAIN";
    ClientRequestType["PARTNER_REQUEST_DELETE_TELEPHONY_DOMAIN"] = "PARTNER_REQUEST_DELETE_TELEPHONY_DOMAIN";
    ClientRequestType["PARTNER_REQUEST_LIST_TELEPHONY_DOMAINS"] = "PARTNER_REQUEST_LIST_TELEPHONY_DOMAINS";
    ClientRequestType["PARTNER_REQUEST_READ_TELEPHONY_DOMAIN"] = "PARTNER_REQUEST_READ_TELEPHONY_DOMAIN";
    ClientRequestType["PARTNER_REQUEST_SET_DEFAULT_TELEPHONY_DOMAIN"] = "PARTNER_REQUEST_SET_DEFAULT_TELEPHONY_DOMAIN";
    ClientRequestType["PARTNER_REQUEST_CREATE_TELEPHONE_NUMBER"] = "PARTNER_REQUEST_CREATE_TELEPHONE_NUMBER";
    ClientRequestType["PARTNER_REQUEST_UPDATE_TELEPHONE_NUMBER"] = "PARTNER_REQUEST_UPDATE_TELEPHONE_NUMBER";
    ClientRequestType["PARTNER_REQUEST_DELETE_TELEPHONE_NUMBER"] = "PARTNER_REQUEST_DELETE_TELEPHONE_NUMBER";
    ClientRequestType["PARTNER_REQUEST_UPLOAD_TELEPHONE_NUMBER_RESOURCE"] = "PARTNER_REQUEST_UPLOAD_TELEPHONE_NUMBER_RESOURCE";
    ClientRequestType["PARTNER_REQUEST_DOWNLOAD_TELEPHONE_NUMBER_RESOURCE"] = "PARTNER_REQUEST_DOWNLOAD_TELEPHONE_NUMBER_RESOURCE";
    ClientRequestType["PARTNER_REQUEST_MAX"] = "PARTNER_REQUEST_MAX";
    ClientRequestType["TELEPHONY_REQUEST_ONCALLIN"] = "TELEPHONY_REQUEST_ONCALLIN";
    ClientRequestType["TELEPHONY_REQUEST_SUBMIT_SESSIONKEY"] = "TELEPHONY_REQUEST_SUBMIT_SESSIONKEY";
    ClientRequestType["TELEPHONY_REQUEST_SUBMIT_PARTICIPANTNUM"] = "TELEPHONY_REQUEST_SUBMIT_PARTICIPANTNUM";
    ClientRequestType["TELEPHONY_REQUEST_ONLEAVE"] = "TELEPHONY_REQUEST_ONLEAVE";
    ClientRequestType["TELEPHONY_REQUEST_ON_SIPGATEWAY_CALL"] = "TELEPHONY_REQUEST_ON_SIPGATEWAY_CALL";
    ClientRequestType["TELEPHONY_REQUEST_POST_SIPGATEWAY_CALL"] = "TELEPHONY_REQUEST_POST_SIPGATEWAY_CALL";
    ClientRequestType["TELEPHONY_REQUEST_ON_TEXT_MESSAGE"] = "TELEPHONY_REQUEST_ON_TEXT_MESSAGE";
    ClientRequestType["TELEPHONY_REQUEST_ON_VOICE_MESSAGE"] = "TELEPHONY_REQUEST_ON_VOICE_MESSAGE";
    ClientRequestType["TELEPHONY_REQUEST_MAX"] = "TELEPHONY_REQUEST_MAX";
    ClientRequestType["ROUTING_ACD_REQUEST_CREATE"] = "ROUTING_ACD_REQUEST_CREATE";
    ClientRequestType["ROUTING_ACD_REQUEST_UPDATE"] = "ROUTING_ACD_REQUEST_UPDATE";
    ClientRequestType["ROUTING_ACD_REQUEST_ACCEPT"] = "ROUTING_ACD_REQUEST_ACCEPT";
    ClientRequestType["ROUTING_ACD_REQUEST_DECLINE"] = "ROUTING_ACD_REQUEST_DECLINE";
    ClientRequestType["ROUTING_ACD_REQUEST_READ_OFFICE_HOUR"] = "ROUTING_ACD_REQUEST_READ_OFFICE_HOUR";
    ClientRequestType["ROUTING_ACD_REQUEST_LEAVE_MESSAGE"] = "ROUTING_ACD_REQUEST_LEAVE_MESSAGE";
    ClientRequestType["ROUTING_ACD_REQUEST_ADD_BOT"] = "ROUTING_ACD_REQUEST_ADD_BOT";
    ClientRequestType["ROUTING_ACD_REQUEST_REMOVE_BOT"] = "ROUTING_ACD_REQUEST_REMOVE_BOT";
    ClientRequestType["ROUTING_SERVICE_REQUEST_SUBSCRIBE"] = "ROUTING_SERVICE_REQUEST_SUBSCRIBE";
    ClientRequestType["ROUTING_SERVICE_REQUEST_LIST"] = "ROUTING_SERVICE_REQUEST_LIST";
    ClientRequestType["ROUTING_SERVICE_REQUEST_CREATE"] = "ROUTING_SERVICE_REQUEST_CREATE";
    ClientRequestType["ROUTING_SERVICE_REQUEST_UPDATE"] = "ROUTING_SERVICE_REQUEST_UPDATE";
    ClientRequestType["ROUTING_REQUEST_MAX"] = "ROUTING_REQUEST_MAX";
    ClientRequestType["SEARCH_REQUEST_INDEX"] = "SEARCH_REQUEST_INDEX";
    ClientRequestType["SEARCH_REQUEST_SEARCH"] = "SEARCH_REQUEST_SEARCH";
    ClientRequestType["SEARCH_REQUEST_MAX"] = "SEARCH_REQUEST_MAX";
    ClientRequestType["SSO_SP_REQUEST_GET"] = "SSO_SP_REQUEST_GET";
    ClientRequestType["SSO_SP_REQUEST_POST"] = "SSO_SP_REQUEST_POST";
    ClientRequestType["SSO_SP_REQUEST_MAX"] = "SSO_SP_REQUEST_MAX";
    ClientRequestType["SSO_IDP_REQUEST_GET"] = "SSO_IDP_REQUEST_GET";
    ClientRequestType["SSO_IDP_REQUEST_POST"] = "SSO_IDP_REQUEST_POST";
    ClientRequestType["SSO_REQUEST_MAX"] = "SSO_REQUEST_MAX";
    ClientRequestType["ACTIVITY_REQUEST_QUERY"] = "ACTIVITY_REQUEST_QUERY";
    ClientRequestType["ACTIVITY_REQUEST_MAX"] = "ACTIVITY_REQUEST_MAX";
    ClientRequestType["CLIENT_USERS_FLOW_ACTION_SUMMARY"] = "CLIENT_USERS_FLOW_ACTION_SUMMARY";
    ClientRequestType["INT_USERS_FLOW_ACTION_SUMMARY"] = "INT_USERS_FLOW_ACTION_SUMMARY";
    ClientRequestType["FLOW_ACTION_LIST"] = "FLOW_ACTION_LIST";
    ClientRequestType["FLOW_BINDER_LIST"] = "FLOW_BINDER_LIST";
    ClientRequestType["EXTERNAL_USERS"] = "EXTERNAL_USERS";
    ClientRequestType["INTERNAL_USERS"] = "INTERNAL_USERS";
    ClientRequestType["WORKSPACES"] = "WORKSPACES";
    ClientRequestType["WORKSPACE_ACTIONS"] = "WORKSPACE_ACTIONS";
    ClientRequestType["GROUP_REQUEST_EXPORT_WORKSPACE_LIST"] = "GROUP_REQUEST_EXPORT_WORKSPACE_LIST";
    ClientRequestType["GROUP_REQUEST_EXPORT_ACTION_LIST"] = "GROUP_REQUEST_EXPORT_ACTION_LIST";
    ClientRequestType["USERS_WITH_OPEN_ACTIONS_CNT"] = "USERS_WITH_OPEN_ACTIONS_CNT";
    ClientRequestType["GROUP_REQUEST_EXPORT_MEETING_USAGE"] = "GROUP_REQUEST_EXPORT_MEETING_USAGE";
    ClientRequestType["GROUP_REQUEST_EXPORT_MEETING_LIST"] = "GROUP_REQUEST_EXPORT_MEETING_LIST";
    ClientRequestType["GROUP_REQUEST_EXPORT_SR_LIST"] = "GROUP_REQUEST_EXPORT_SR_LIST";
})(ClientRequestType || (ClientRequestType = {}));
