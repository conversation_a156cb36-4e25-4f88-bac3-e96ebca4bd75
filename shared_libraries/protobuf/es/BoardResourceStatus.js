export var BoardResourceStatus;
(function (BoardResourceStatus) {
    BoardResourceStatus["BOARD_RESOURCE_STATUS_NONE"] = "BOARD_RESOURCE_STATUS_NONE";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_QUEUED"] = "BOARD_RESOURCE_STATUS_QUEUED";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_CONVERTING"] = "BOARD_RESOURCE_STATUS_CONVERTING";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_CONVERTED"] = "BOARD_RESOURCE_STATUS_CONVERTED";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_CONVERT_FAILED"] = "BOARD_RESOURCE_STATUS_CONVERT_FAILED";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_KEEP_UNCONVERTED"] = "BOARD_RESOURCE_STATUS_KEEP_UNCONVERTED";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_TOO_MANY_PAGES"] = "BOARD_RESOURCE_STATUS_TOO_MANY_PAGES";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_TOO_LARGE"] = "BOARD_RESOURCE_STATUS_TOO_LARGE";
    BoardResourceStatus["BOARD_RESOURCE_STATUS_INVALID_PASSWORD"] = "BOARD_RESOURCE_STATUS_INVALID_PASSWORD";
})(BoardResourceStatus || (BoardResourceStatus = {}));
