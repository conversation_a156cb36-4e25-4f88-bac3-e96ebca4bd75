import {User} from './User'
export interface SRAgentSummary {
  timestamp?: number
  user?: User
  assigned_to_other_number?: number
  assigned_to_self_number?: number
  total_assignment_time?: number
  assigned_no_response_number?: number
  return_to_inbox_number?: number
  resolve_by_self_number?: number
  close_by_client_number?: number
  total_msg_file_number?: number
  total_resolution_time?: number
  active_sr_number?: number
  client_reopen_number?: number
  assigned_to_self_number_new?: number
  reassigned_number?: number
}
