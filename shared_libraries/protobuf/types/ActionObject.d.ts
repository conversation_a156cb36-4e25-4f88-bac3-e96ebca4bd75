import {ExtCalType} from './ExtCalType'
import {ActionPageSwitch} from './ActionPageSwitch'
import {ActionLaserPointer} from './ActionLaserPointer'
import {ActionUserPointer} from './ActionUserPointer'
import {ActionUserRoster} from './ActionUserRoster'
import {ActionUserRosterKeepAliveInfo} from './ActionUserRosterKeepAliveInfo'
import {ActionUserRosterEvent} from './ActionUserRosterEvent'
import {ActionVideoState} from './ActionVideoState'
import {AudioConf} from './AudioConf'
import {DesktopShareConf} from './DesktopShareConf'
import {DesktopShareState} from './DesktopShareState'
import {SessionRecordingState} from './SessionRecordingState'
import {SessionAudioState} from './SessionAudioState'
import {SessionVideoState} from './SessionVideoState'
import {TelephonyConf} from './TelephonyConf'
import {SessionStatus} from './SessionStatus'
import {VendorServiceType} from './VendorServiceType'
export interface ActionObject {
  board_id?: string
  original_board_id?: string
  parent_board_id?: string
  session_key?: string
  session_password?: string
  password_protected?: boolean
  ext_cal_event_id?: string
  ext_cal_type?: ExtCalType
  topic?: string
  agenda?: string
  isnote?: boolean
  is_private?: boolean
  page_switch?: ActionPageSwitch
  laser_pointer?: ActionLaserPointer
  user_pointer?: ActionUserPointer[]
  team_roster?: ActionUserRoster[]
  user_roster?: ActionUserRoster[]
  keepalive_info?: ActionUserRosterKeepAliveInfo[]
  events?: ActionUserRosterEvent[]
  total_rosters?: number
  video_state?: ActionVideoState[]
  audio_conf?: AudioConf
  zone?: string
  is_expired?: boolean
  is_locked?: boolean
  last_modified_time?: number
  revision?: number
  is_deleted?: boolean
  last_deletion_revision?: number
  local_revision?: number
  assignments?: string
  timestamp?: number
  ds_conf?: DesktopShareConf
  ds_state?: DesktopShareState[]
  recording_state?: SessionRecordingState[]
  audio_state?: SessionAudioState[]
  session_video_state?: SessionVideoState[]
  telephony_conf?: TelephonyConf
  scheduled_start_time?: number
  scheduled_end_time?: number
  session_status?: SessionStatus
  start_time?: number
  end_time?: number
  milliseconds_allowed_to_join_before_start?: number
  record_multiple_video_channel?: boolean
  auto_recording?: boolean
  enable_ringtone?: boolean
  reminder_interval?: number
  vendor_service_type?: VendorServiceType
  vendor_start_url?: string
  vendor_join_url?: string
  vendor_meet_id?: string
  vendor_service_owner?: string
  vendor_occurrence_id?: string
  recording?: number
  transcription?: number
  transcription_vtt?: number
  meet_summary?: number
  meet_summary_edited?: boolean
  audio_speaker?: number
  meet_chat?: number
  timezone?: string
  dtstart?: number
  rrule?: string
  exdate?: string
  location?: string
  total_events?: number
  active_speaker_id?: string
  created_time?: number
  updated_time?: number
}
