export interface GroupSetting {
  content_editable_interval?: number
  content_editable_interval_for_client?: number
  org_invitation_expiry?: number
  enable_private_meet?: boolean
  hide_meet_recording?: boolean
  disable_meet_recording_sharing?: boolean
  enable_meet_auto_recording?: boolean
  enable_meet_password?: boolean
  meeting_default_password?: string
  enable_mobile_web_meeting_join?: boolean
  enable_workflow_event?: boolean
  enable_digest_email?: boolean
  digest_email_start_timestamp?: number
  digest_email_interval?: number
  enable_client_self_signup?: boolean
  send_service_request_on_client_self_signup?: boolean
  enable_client_group?: boolean
  enable_content_library?: boolean
  enable_client_resources?: boolean
  enable_action_library?: boolean
  enable_broadcast?: boolean
  user_logins_max?: number
  expose_contact_info_to_clients?: boolean
  expose_client_contact_info_to_internals?: boolean
  enable_flow_template_library?: boolean
  account_lock_duration?: number
  enforce_signature_jwt_validation_for_client_users?: boolean
  enable_magic_link?: boolean
  enable_magic_link_for_internal_users?: boolean
  binder_view_token_timeout?: number
  binder_view_token_timeout_for_internal_users?: number
  use_browser_open_jwt?: boolean
  user_boards_auto_archive_threshold?: number
  terms_group_name?: string
  enable_meet_log_uploading?: boolean
  enable_user_data_downloading?: boolean
  include_coc_in_signed_file?: boolean
  enable_workspace_report_auditing?: boolean
  enable_sms_for_email_based_client_user?: boolean
  enable_client_distribution_list?: boolean
  enable_broadcast_recipient?: boolean
  enable_inbox?: boolean
  enable_ai?: boolean
  enable_client_group_to_internal_users?: boolean
}
