import {User} from './User'
import {Group} from './Group'
import {Partner} from './Partner'
import {SystemSamlService} from './SystemSamlService'
import {TelephonyDomain} from './TelephonyDomain'
import {Board} from './Board'
import {WebApp} from './WebApp'
import {ObjectRecording} from './ObjectRecording'
import {UsageStatistics} from './UsageStatistics'
import {CapacityReport} from './CapacityReport'
import {SystemConfig} from './SystemConfig'
import {ActionObject} from './ActionObject'
import {AudioRecording} from './AudioRecording'
import {Presence} from './Presence'
import {Contacts} from './Contacts'
import {ActivityLog} from './ActivityLog'
import {ActivityStatistics} from './ActivityStatistics'
import {ClientRequest} from './ClientRequest'
import {CacheObjectChange} from './CacheObjectChange'
export interface CacheObject {
  user?: User
  group?: Group
  partner?: Partner
  saml_service?: SystemSamlService
  telephony_domain?: TelephonyDomain
  board?: Board
  webapp?: WebApp
  recording?: ObjectRecording
  usage?: UsageStatistics
  audio_report?: CapacityReport
  ds_report?: CapacityReport
  video_report?: CapacityReport
  system_config?: SystemConfig
  session?: ActionObject
  audio?: AudioRecording
  presence?: Presence
  contacts?: Contacts
  revision?: number
  previous_revision?: number
  updated_time?: number
  local_revision?: number
  activity_logs?: ActivityLog[]
  activity_stats?: ActivityStatistics
  local_changes?: ClientRequest[]
  latest_changes?: CacheObjectChange[]
  request_id?: string
}
