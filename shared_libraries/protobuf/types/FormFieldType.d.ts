export declare enum FormFieldType {
    FORM_FIELD_TYPE_INVLIAD = "FORM_FIELD_TYPE_INVLIAD",
    FORM_FIELD_TYPE_PUSH_BUTTON = "FORM_FIELD_TYPE_PUSH_BUTTON",
    FORM_FIELD_TYPE_RADIO_BUTTON = "FORM_FIELD_TYPE_RADIO_BUTTON",
    FORM_FIELD_TYPE_CHECKBOX = "FORM_FIELD_TYPE_CHECKBOX",
    FORM_FIELD_TYPE_TEXT = "FORM_FIELD_TYPE_TEXT",
    FORM_FIELD_TYPE_CHOICE = "FORM_FIELD_TYPE_CHOICE",
    FORM_FIELD_TYPE_SIGNATURE = "FORM_FIELD_TYPE_SIGNATURE"
}
//# sourceMappingURL=FormFieldType.d.ts.map