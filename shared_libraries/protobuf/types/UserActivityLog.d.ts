import {UserType} from './UserType'
import {User} from './User'
import {UserActivityLogEntry} from './UserActivityLogEntry'
import {UserEngagementQuery} from './UserEngagementQuery'
import {QueryFilter} from './QueryFilter'
export interface UserActivityLog {
  actor_org_id?: string
  actor_id?: string
  actor_email?: string
  actor_unique_id?: string
  actor_phone_number?: string
  actor_type?: UserType
  actor?: User
  suppress_stats?: boolean
  activities?: UserActivityLogEntry[]
  actor_ids?: string[]
  from_date?: number
  to_date?: number
  action_group_id?: string[]
  action_type_id?: string[]
  user_engagement_query?: UserEngagementQuery[]
  page_start?: number
  page_size?: number
  page_number?: number
  sort_field_name?: string
  sort_method?: string
  filters?: QueryFilter[]
}
