export interface AccessToken {
  board_id?: string
  device_id?: string
  client_id?: string
  agent_id?: string
  access_token?: string
  access_token_expire_timestamp?: number
  access_token_created_timestamp?: number
  uid?: string
  token?: string
  token_from_cookie?: boolean
  session_id?: string
  session_id_from_cookie?: boolean
  connection_id?: string
  email?: string
  client_ip?: string
  server_ip?: string
  awselb?: string
  request_url?: string
  host?: string
  referer?: string
  client_private_ip?: string
  origin?: string
  connection_number?: string
  client_ua?: string
  client_version?: number
  client_accept_language?: string
  gid?: string
  name?: string
  x_forwarded_uri?: string
  token_verified?: boolean
  agent_token_verified?: boolean
  server_token_verified?: boolean
  websocket?: boolean
  board_access_token_verified?: boolean
  scope?: number
  created_time?: number
}
