{"name": "@mx-next/shared", "version": "0.0.1", "main": "lib/index.js", "author": "colin", "license": "MIT", "type": "module", "files": ["lib"], "exports": {".": {"import": "./lib/index.js", "types": "./lib/index.d.ts"}, "./universal": {"import": "./lib/universal.js", "types": "./lib/universal.d.ts"}, "./esm": {"import": "./lib/esm/index.js", "types": "./lib/esm/index.d.ts"}}, "publishConfig": {"access": "public"}, "scripts": {"precommit": "lint-staged", "build": "rm -rf lib/ && tsc -p tsconfig.json", "build:test": "pnpm build", "dev": "tsc -p tsconfig.json --watch --preserveWatchOutput --incremental", "lint": "eslint --ext .ts src", "lint:report": "pnpm lint --format json --output-file report.json", "prepack": "pnpm build", "test": "vitest src", "test:ci": "pnpm run test --silent --coverage"}, "devDependencies": {"@jest/globals": "^29.7.0", "@silverhand/eslint-config": "6.0.1", "@silverhand/ts-config": "6.0.0", "@types/node": "^20.9.5", "@vitest/coverage-v8": "^2.1.8", "eslint": "^8.56.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "typescript": "^5.5.3", "vitest": "^2.1.8"}, "engines": {"node": "^20.9.0"}, "eslintConfig": {"extends": "@silverhand", "rules": {"@typescript-eslint/ban-types": "off"}}, "prettier": "@silverhand/eslint-config/.prettierrc", "dependencies": {"@silverhand/essentials": "^2.9.2", "chalk": "^5.3.0"}}