import chalk from 'chalk';
export default class ConsoleLog {
    static { this.prefixes = Object.freeze({
        info: chalk.bold(chalk.blue('info')),
        warn: chalk.bold(chalk.yellow('warn')),
        error: chalk.bold(chalk.red('error')),
        fatal: chalk.bold(chalk.red('fatal')),
    }); }
    constructor(
    /** A prefix to prepend to all log messages. */
    prefix, 
    /**
     * The number of spaces to pad the prefix. For example, if the prefix is `custom` and the
     * padding is 8, the output will be `custom  `.
     *
     * @default 8
     */
    padding = 8) {
        this.prefix = prefix;
        this.padding = padding;
        this.plain = (...args) => {
            console.log(...this.getArgs(args));
        };
        this.info = (...args) => {
            this.plain(ConsoleLog.prefixes.info, ...args);
        };
        this.succeed = (...args) => {
            this.info(chalk.green('✔'), ...args);
        };
        this.warn = (...args) => {
            console.warn(...this.getArgs([ConsoleLog.prefixes.warn, ...args]));
        };
        this.error = (...args) => {
            console.error(...this.getArgs([ConsoleLog.prefixes.error, ...args]));
        };
        this.fatal = (...args) => {
            console.error(...this.getArgs([ConsoleLog.prefixes.fatal, ...args]));
            // eslint-disable-next-line unicorn/no-process-exit
            process.exit(1);
        };
    }
    getArgs(args) {
        if (this.prefix) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
            return [this.prefix.padEnd(this.padding), ...args];
        }
        return args;
    }
}
