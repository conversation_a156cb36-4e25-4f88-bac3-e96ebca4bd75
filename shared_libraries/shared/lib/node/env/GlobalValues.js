import { getEnv, yes } from '@silverhand/essentials';
export default class GlobalValues {
    constructor() {
        this.isProduction = getEnv('NODE_ENV') === 'production';
        this.isIntegrationTest = yes(getEnv('INTEGRATION_TEST'));
        this.isUnitTest = getEnv('NODE_ENV') === 'test';
        this.isDevFeaturesEnabled = !this.isProduction || yes(getEnv('DEV_FEATURES_ENABLED'));
        this.httpsCert = process.env.HTTPS_CERT_PATH;
        this.httpsKey = process.env.HTTPS_KEY_PATH;
        this.isHttpsEnabled = Boolean(this.httpsCert && this.httpsKey);
    }
}
