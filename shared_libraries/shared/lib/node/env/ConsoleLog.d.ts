export default class ConsoleLog {
    /** A prefix to prepend to all log messages. */
    readonly prefix?: string | undefined;
    /**
     * The number of spaces to pad the prefix. For example, if the prefix is `custom` and the
     * padding is 8, the output will be `custom  `.
     *
     * @default 8
     */
    readonly padding: number;
    static prefixes: Readonly<{
        info: string;
        warn: string;
        error: string;
        fatal: string;
    }>;
    constructor(
    /** A prefix to prepend to all log messages. */
    prefix?: string | undefined, 
    /**
     * The number of spaces to pad the prefix. For example, if the prefix is `custom` and the
     * padding is 8, the output will be `custom  `.
     *
     * @default 8
     */
    padding?: number);
    plain: typeof console.log;
    info: typeof console.log;
    succeed: typeof console.log;
    warn: typeof console.log;
    error: typeof console.log;
    fatal: (...args: Parameters<typeof console.log>) => never;
    protected getArgs(args: Parameters<typeof console.log>): [message?: any, ...optionalParams: any[]];
}
